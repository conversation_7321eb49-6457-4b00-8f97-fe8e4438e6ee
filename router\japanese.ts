import { Router } from "express";
import * as kuromoji from "kuromoji";

const router = Router();

/**
 * @swagger
 * /japanese/tokenize:
 *   get:
 *     summary: Tokenize Japanese text
 *     description: Split Japanese text into tokens using kuromoji
 *     tags: [Japanese]
 *     parameters:
 *       - in: query
 *         name: text
 *         schema:
 *           type: string
 *         required: true
 *         description: Japanese text to tokenize
 *     responses:
 *       200:
 *         description: Successfully tokenized text
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: number
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Success"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *       400:
 *         description: Missing text parameter
 *       500:
 *         description: Error tokenizing text
 */
let tokenizer: any = null;
kuromoji.builder({ dicPath: "node_modules/kuromoji/dict" }).build((err, builtTokenizer) => {
  if (err) {
    console.error("Lỗi khi tải từ điển:", err);
    return;
  }
  tokenizer = builtTokenizer;
  console.log("✅ Tokenizer đã sẵn sàng!");
});

router.post("/tokenize", async (req, res) => {
   if (!tokenizer) {
    return res.status(503).json({ error: "Tokenizer chưa sẵn sàng" });
  }

  const { sentence } = req.body;

  if (!sentence || typeof sentence !== "string") {
    return res.status(400).json({ error: "Thiếu hoặc sai định dạng 'sentence'" });
  }

  const tokens = tokenizer.tokenize(sentence);

  // Chuyển đổi kết quả cho dễ dùng
  const result = tokens.map((token: any) => ({
    surface: token.surface_form,
    basic_form: token.basic_form,
    pos: token.pos,
    pos_detail_1: token.pos_detail_1,
    reading: token.reading,
    pronunciation: token.pronunciation,
  }));

  res.json({ tokens: result });
});

export default router;