import { Router } from "express";
import * as kuromoji from "kuromoji";

const router = Router();

/**
 * @swagger
 * /japan/tokenize:
 *   post:
 *     summary: Tokenize Japanese text
 *     description: Split Japanese text into tokens using kuromoji
 *     tags: [Japanese]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               sentence:
 *                 type: string
 *                 description: Japanese text to tokenize
 *                 example: "こんにちは世界"
 *             required:
 *               - sentence
 *     responses:
 *       200:
 *         description: Successfully tokenized text
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 tokens:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       surface:
 *                         type: string
 *                         description: Surface form of the token
 *                       basic_form:
 *                         type: string
 *                         description: Basic form of the token
 *                       pos:
 *                         type: string
 *                         description: Part of speech
 *                       pos_detail_1:
 *                         type: string
 *                         description: Detailed part of speech
 *                       reading:
 *                         type: string
 *                         description: Reading of the token
 *                       pronunciation:
 *                         type: string
 *                         description: Pronunciation of the token
 *       400:
 *         description: Missing or invalid sentence parameter
 *       503:
 *         description: Tokenizer not ready
 */
let tokenizer: any = null;
kuromoji.builder({ dicPath: "node_modules/kuromoji/dict" }).build((err, builtTokenizer) => {
  if (err) {
    console.error("Lỗi khi tải từ điển:", err);
    return;
  }
  tokenizer = builtTokenizer;
  console.log("✅ Tokenizer đã sẵn sàng!");
});

router.post("/tokenize", async (req, res) => {
   if (!tokenizer) {
    return res.status(503).json({ error: "Tokenizer chưa sẵn sàng" });
  }

  const { sentence } = req.body;

  if (!sentence || typeof sentence !== "string") {
    return res.status(400).json({ error: "Thiếu hoặc sai định dạng 'sentence'" });
  }

  const tokens = tokenizer.tokenize(sentence);

  // Chuyển đổi kết quả cho dễ dùng
  const result = tokens.map((token: any) => ({
    surface: token.surface_form,
    basic_form: token.basic_form,
    pos: token.pos,
    pos_detail_1: token.pos_detail_1,
    reading: token.reading,
    pronunciation: token.pronunciation,
  }));

  res.json({ tokens: result });
});

export default router;