import { Router } from "express";
import * as kuromoji from "kuromoji";
import * as path from "path";

const router = Router();

/**
 * @swagger
 * /japan/tokenize:
 *   post:
 *     summary: Tokenize Japanese text
 *     description: Split Japanese text into tokens using kuromoji
 *     tags: [Japanese]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               sentence:
 *                 type: string
 *                 description: Japanese text to tokenize
 *                 example: "こんにちは世界"
 *             required:
 *               - sentence
 *     responses:
 *       200:
 *         description: Successfully tokenized text
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 tokens:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       surface:
 *                         type: string
 *                         description: Surface form of the token
 *                       basic_form:
 *                         type: string
 *                         description: Basic form of the token
 *                       pos:
 *                         type: string
 *                         description: Part of speech
 *                       pos_detail_1:
 *                         type: string
 *                         description: Detailed part of speech
 *                       reading:
 *                         type: string
 *                         description: Reading of the token
 *                       pronunciation:
 *                         type: string
 *                         description: Pronunciation of the token
 *       400:
 *         description: Missing or invalid sentence parameter
 *       503:
 *         description: Tokenizer not ready
 */
let tokenizer: any = null;

// Tạo danh sách các đường dẫn dictionary có thể có
const possibleDicPaths = [
  path.join(__dirname, "dict"), // Dictionary đã copy vào dist
  path.join(process.cwd(), "dist", "dict"), // Dictionary trong dist từ root
  path.join(process.cwd(), "node_modules", "kuromoji", "dict"),
  path.join(__dirname, "..", "node_modules", "kuromoji", "dict"),
  path.join(__dirname, "..", "..", "node_modules", "kuromoji", "dict"),
  "node_modules/kuromoji/dict",
  "./node_modules/kuromoji/dict"
];

// Thử từng đường dẫn cho đến khi tìm được
let dicPathIndex = 0;

function tryBuildTokenizer() {
  if (dicPathIndex >= possibleDicPaths.length) {
    console.error("❌ Không thể tìm thấy dictionary kuromoji ở bất kỳ đường dẫn nào");
    return;
  }

  const dicPath = possibleDicPaths[dicPathIndex];
  console.log(`🔍 Thử đường dẫn dictionary: ${dicPath}`);

  kuromoji.builder({ dicPath }).build((err, builtTokenizer) => {
    if (err) {
      console.error(`❌ Lỗi với đường dẫn ${dicPath}:`, err.message);
      dicPathIndex++;
      tryBuildTokenizer(); // Thử đường dẫn tiếp theo
      return;
    }
    tokenizer = builtTokenizer;
    console.log("✅ Tokenizer đã sẵn sàng!");
    console.log("📁 Dictionary path thành công:", dicPath);
  });
}

// Bắt đầu thử build tokenizer
tryBuildTokenizer();

router.post("/tokenize", async (req, res) => {
   if (!tokenizer) {
    return res.status(503).json({ error: "Tokenizer chưa sẵn sàng" });
  }

  const { sentence } = req.body;

  if (!sentence || typeof sentence !== "string") {
    return res.status(400).json({ error: "Thiếu hoặc sai định dạng 'sentence'" });
  }

  const tokens = tokenizer.tokenize(sentence);

  // Chuyển đổi kết quả cho dễ dùng
  const result = tokens.map((token: any) => ({
    surface: token.surface_form,
    basic_form: token.basic_form,
    pos: token.pos,
    pos_detail_1: token.pos_detail_1,
    reading: token.reading,
    pronunciation: token.pronunciation,
  }));

  res.json({ tokens: result });
});

export default router;