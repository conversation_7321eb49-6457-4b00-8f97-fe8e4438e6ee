import express from "express";
import cors from "cors";
import { createServer } from "http";
import { Server } from "socket.io";
import { createClient } from "redis";
import { REDIS_HOST, REDIS_PORT } from "./config";
import { setupSwagger } from "./swagger";

const app = express();
// Khởi động Swagger
setupSwagger(app);
const port = process.env.PORT || 3000;
var server = createServer(app);
var io = new Server(server, {
  cors: { origin: "*" },
  allowEIO3: true,
});

app.use(express.json({ limit: "50mb" }));

app.use(
  cors({
    origin: "*",
  })
);

export const redis = createClient({
  socket: {
    host: REDIS_HOST,
    port: REDIS_PORT,
  },
  // password: "81951ebfb79a444882431419cc4c66a0",
});


redis.connect();

//
// load initModuleIndex function
import dataRouter from "./router/data";
import settingRouter from "./router/setting";
import winiRouter from "./router/wini";
import supperAppRouter from "./router/supperApp";
import integrationRouter from "./router/intergration";
import ebigRouter from "./router/ebig";
import uploadFileRoute from "./router/uploadFile";
import tinhThanhRoute from "./router/tinhthanhvietnam";
import vnPayRouter from "./router/payment";
import crudDA from "./da/crudDA";
import japaneseRouter from "./router/japanese";

app.use("/api/wini", winiRouter);
app.use("/api/setting", settingRouter);
app.use("/api/data", dataRouter);
app.use("/api/supperApp", supperAppRouter);
app.use("/api/intergration", integrationRouter);
app.use("/api/ebig", ebigRouter);
app.use("/api/file", uploadFileRoute);
app.use("/api/tinhthanh", tinhThanhRoute);
app.use("/api/vnpay", vnPayRouter);
app.use("/api/japan", japaneseRouter);

// process.env.PORT
server.listen(port, () => {
  console.log(`Server is running on http://localhost:${port}`);
  console.log(`Swagger docs available at http://localhost:${port}/api-docs`);
});

const onlineUsers = new Map(); // Lưu trữ userId -> socketId
const lastActive = new Map(); // Lưu trữ userId -> thời gian hoạt động cuối
const _conversationController = new crudDA(`data:f5e4a5074091423981f047cf9f883175:Conversation`);
const _participantController = new crudDA(`data:f5e4a5074091423981f047cf9f883175:Participant`);
const _messageController = new crudDA(`data:1052ecc72a7d47f2947f2c66203cf84b:Message`);

io.on("connection", function (socket) {
  console.log("ID ket noi server: " + socket.id);
  socket.emit(socket.id);
  const { peopleid } = socket.handshake.headers;
  onlineUsers.set(peopleid, socket.id);

  ///
  io.to(socket.id).emit("online", peopleid);

  //#region Project
  socket.on("client-init", function (data) {
    if (data.pageId) socket.join("pageid-" + data.pageId);
    if (data.cardId) socket.join("cardid-" + data.cardId);
    if (data.viewId) socket.join("viewid-" + data.viewId);
  });

  socket.on("client-main", function (data) {
    var pid = parseInt(data.pid);
    // var headers = {
    //     'pageid': data.pageid,
    //     'pid': data.pid,
    //     'parentid': data.parentid,
    //     'oid': data.enumObj,
    //     'eid': data.enumEvent,
    //     'token': data.token,
    //     'Content-Type': "application/json"
    // };
    io.to("project_" + pid.toString()).emit("server-main", data);
    socket.emit("server-log", "");
    io.to(data.userId).emit("server-google", data);
  });
  //#endregion
  //#region Mouse
  socket.on("client-mouse", function (data) {
    try {
      socket.to("project_" + data.pid.toString()).emit("server-mouse", data);
    } catch (e) {}
  });
  socket.on("page-demo", function (data) {
    if (data.pageId) socket.to("pageid-" + data.pageId).emit("page-demo", data);
    if (data.cardId) socket.to("cardid-" + data.cardId).emit("page-demo", data);
    if (data.viewId) socket.to("viewid-" + data.viewId).emit("page-demo", data);
  });
  //#endregion

  //#region Chat Application
  // Nhận tin nhắn 1-1
  socket.on("send_message", async (data) => {
    const { peopleid } = data;
    try {
      const userSocketId = onlineUsers.get(peopleid);
      if (userSocketId) {
        io.to(userSocketId).emit("receive_message", data);
      }
      // lưu tin nhắn vào db
      await _messageController.action("add", [data]);
    } catch (error: any) {
      console.error("Error sending message:", error);
    }
  });
  //#region Room
  // Nhắn tin nhóm
  socket.on("room_message", (data) => {
    const { id, peopleid, message } = data;
    io.to(id).emit("room_message", { peopleid, message });
  });

  // Người dùng tham gia nhóm
  socket.on("join_room", (data) => {
    const { id } = data;
    socket.join(id);
  });

  // Người dùng rời nhóm
  socket.on("leave_room", (data) => {
    const { id } = data;
    socket.leave(id);
  });
  //#endregion

  socket.on("userconnect", (data) => {
    socket.join(data.id);
    io.in(data.id).emit("inform_others_about_me", data);
  });

  socket.on("SDPProcess", (data) => {
    socket.to(data.id).emit("SDPProcess", data);
  });

  socket.on("typing", ({ targetUserIds, senderId }) => {
    targetUserIds.forEach((targetUserId: any) => {
      const targetSocketId = onlineUsers.get(targetUserId); // Lấy socketId từ Map
      if (targetSocketId) {
        io.to(targetSocketId).emit("userTyping", { senderId });
      }
    });
  });
  //#endregion
  //#region Disconnect
  socket.on("disconnect", async function () {
    console.log("disconnect " + socket.id);
    const _customerController = new crudDA(`data:f5e4a5074091423981f047cf9f883175:Customer`);
    const _friendController = new crudDA("data:f5e4a5074091423981f047cf9f883175:Friend");
    // await redis.set(`user:${socket.id}:status`, 'offline');
    for (const [userId, socketId] of onlineUsers.entries()) {
      if (socketId === socket.id) {
        onlineUsers.delete(userId);

        if (userId) {
          _customerController.action("edit", [{ Id: userId, LastActive: Date.now() }]);
          lastActive.set(userId, Date.now());
        }
        const friendRes: any = await _friendController.search(1, 20, `(@CustomerId:{${userId}}) | (@FriendId:{${userId}}) @Status:[1 1]`);
        const listUser = friendRes.data
          .map((e: any) => {
            if (e.CustomerId === userId) return e.FriendId;
            if (e.FriendId === userId) return e.CustomerId;
          })
          .filter((id: any, index: any, arr: any) => arr.indexOf(id) === index);
        // io.emit("update_user_status", { userId, status: 0, lastActiveTime });
        io.emit("online-users", Array.from(onlineUsers.keys()));
        const lastActiveTime = lastActive.get(userId);
        socket.to(listUser).emit("last_active", { userId, lastActiveTime });
      }
    }
  });
  //#endregion
});
