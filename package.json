{"main": "dist/index.js", "scripts": {"start": "ts-node index.ts", "build": "tsc && node scripts/copy-kuromoji-dict.js", "build:clean": "tsc", "serve": "node dist/index.js", "copy-dict": "node scripts/copy-kuromoji-dict.js"}, "dependencies": {"@types/kuromoji": "^0.1.3", "@types/nodemailer": "^6.4.17", "axios": "^1.7.2", "bcrypt": "^5.1.1", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.5.0", "express": "^4.19.2", "firebase-admin": "^13.0.2", "fs-extra": "^11.2.0", "google-auth-library": "^9.11.0", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.1.0", "kuromoji": "^0.1.2", "moment": "^2.30.1", "multer": "^1.4.5-lts.1", "nodemailer": "^6.10.0", "os": "^0.1.2", "path": "^0.12.7", "redis": "^4.6.14", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "unzipper": "^0.12.2", "winston": "^3.13.1"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/fs-extra": "^11.0.4", "@types/multer": "^1.4.12", "@types/node": "^20.14.10", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.7", "@types/unzipper": "^0.10.9", "@types/uuid": "^10.0.0", "eslint": "^9.6.0", "ts-node": "^10.9.2", "typescript": "^5.5.3"}}