const fs = require('fs-extra');
const path = require('path');

async function copyKuromojiDict() {
  try {
    const sourceDir = path.join(__dirname, '..', 'node_modules', 'kuromoji', 'dict');
    const targetDir = path.join(__dirname, '..', 'dist', 'dict');
    
    console.log('📁 Copying kuromoji dictionary...');
    console.log('Source:', sourceDir);
    console.log('Target:', targetDir);
    
    // Kiểm tra xem source directory có tồn tại không
    if (!await fs.pathExists(sourceDir)) {
      throw new Error(`Source directory not found: ${sourceDir}`);
    }
    
    // Copy dictionary
    await fs.copy(sourceDir, targetDir);
    
    console.log('✅ Kuromoji dictionary copied successfully!');
  } catch (error) {
    console.error('❌ Error copying kuromoji dictionary:', error.message);
    process.exit(1);
  }
}

copyKuromojiDict();
