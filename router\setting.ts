import { Router } from "express";
import crudDA from "../da/crudDA";
import { SchemaFieldTypes } from "redis";
import * as contructorDA from "../da/settingDA";
import tableDA from "../da/tableDA";
import { JwtPayload } from "jsonwebtoken";
import login from "../function/login";
import { randomGID } from "../Ultis/convert";

const router = Router();
const _Login = new login();
const settingModules: Array<{ name: string; schema: { [p: string]: SchemaFieldTypes } }> = [
  {
    name: "table",
    schema: contructorDA.tbSchemaConstructor,
  },
  {
    name: "rel",
    schema: contructorDA.relSchemaConstructor,
  },
  {
    name: "column",
    schema: contructorDA.colSchemaConstructor,
  },
  {
    name: "menu",
    schema: contructorDA.menuSchemaConstructor,
  },
  {
    name: "page",
    schema: contructorDA.pageConstructor,
  },
  {
    name: "layer",
    schema: contructorDA.layerConstructor,
  },
  {
    name: "designtoken",
    schema: contructorDA.designTokenConstructor,
  },
  {
    name: "workflow",
    schema: contructorDA.workflowConstructor,
  },
  {
    name: "stage",
    schema: contructorDA.stageConstructor,
  },
  {
    name: "settingstage",
    schema: contructorDA.settingStageConstructor,
  },
];

const settingDataModules: Array<{ name: string; schema: { [p: string]: SchemaFieldTypes } }> = [
  {
    name: "data_form",
    schema: contructorDA.formSchemaConstructor,
  },
  {
    name: "data_card",
    schema: contructorDA.cardSchemaConstructor,
  },
  {
    name: "data_chart",
    schema: contructorDA.chartCardConstructor,
  },
  {
    name: "data_view",
    schema: contructorDA.viewSchemaConstructor,
  },
  {
    name: "data_report",
    schema: contructorDA.reportModelSchemaConstructor,
  },
];

const unAuthorizedUrl = ["/getById", "/getByIds", "/getListSimple", "/getAll"];

// run first time when redis isReady
export function initProjectSettingIdx(pid: string) {
  const _projectDataModule = new tableDA(`setting:${pid}`);
  _projectDataModule.removeIndex().then(async (_) => {
    await _projectDataModule.buildIndex({
      Name: SchemaFieldTypes.TEXT,
      Description: SchemaFieldTypes.TEXT,
      DateCreated: SchemaFieldTypes.NUMERIC,
      Sort: SchemaFieldTypes.NUMERIC,
    });
  });
  settingModules.forEach((e) => {
    const _tbModule = new tableDA(`setting:${pid}:${e.name}`);
    _tbModule.removeIndex().then(async (_) => {
      await _tbModule.buildIndex(e.schema);
    });
  });
  settingDataModules.forEach((e) => {
    const _tbDataModule = new tableDA(`data:${pid}:${e.name}`);
    _tbDataModule.removeIndex().then(async (_) => {
      await _tbDataModule.buildIndex(e.schema);
    });
  });
}

export function createDefaultTable(pid: string) {
  const _tableRepo = new crudDA(`setting:${pid}:table`);
  const _colRepo = new crudDA(`setting:${pid}:column`);
  _tableRepo.action(
    "add",
    contructorDA.projectDefaultData.map((e) => e.tb)
  );
  _colRepo.action("add", contructorDA.projectDefaultData.map((e) => e.cols).flat(Infinity));
  contructorDA.projectDefaultData.forEach((e) => {
    const _moduleRepo = new tableDA(e.tb.Name);
    _moduleRepo.removeIndex().then(async (_) => {
      _moduleRepo.buildIndex(e.index as any);
    });
  });
}

router.use(async (req, res, next) => {
  const { pid, module, authorization } = req.headers;
  const _m = module && settingModules.find((e) => e.name === module);
  if (_m && pid) {
    if (unAuthorizedUrl.includes(req.url.substring(0, req.url.indexOf("?") > 0 ? req.url.indexOf("?") : req.url.length))) return next();
    if (authorization?.startsWith("Bearer")) {
      try {
        const now = new Date();
        const payload = _Login.verifyToken(authorization.replace("Bearer", "").trim()) as JwtPayload;
        if (!payload.exp || now.getTime() > payload.exp * 1000) {
          return res.send({ code: 401, message: "Token is expired" });
        } else return next();
      } catch (error) {
        return res.send({ code: 401, message: "Unauthorized" });
      }
    } else return res.send({ code: 401, message: "Unauthorized" });
  }
  return res.send({ code: 404, message: "pid or module is not found!" });
});

// initProjectSettingIdx("1db6d3afe3c442a7a1366dffa0cea2e0");
// initProjectSettingIdx("dd757d35dbd245df843b1f54ada656ff");
// initProjectSettingIdx("944b6a5577524e1eadef54ab63598daa");
// initProjectSettingIdx("9175116b26af4761859010aadff08b8f");
// initProjectSettingIdx("f5e4a5074091423981f047cf9f883175");
// initProjectSettingIdx("c344f5c7a52a4a749ee2e49e9c356a3b");

//#region unAuthorized
router.get("/getById", async (req, res) => {
  const { pid, module } = req.headers;
  const { id } = req.query;
  const _moduleRepo = new crudDA(`setting:${pid}:${module}`);
  if (id) {
    const results = await _moduleRepo.getById(id as string);
    return res.status(200).json({ data: results, code: 200, message: "Success" });
  } else return res.send({ code: 404, message: "Missing id" });
});

router.post("/getByIds", async (req, res) => {
  const { pid, module } = req.headers;
  const { ids } = req.body;
  const _moduleRepo = new crudDA(`setting:${pid}:${module}`);
  if (ids) {
    if (!ids.length) res.status(200).json({ data: [], code: 200, message: "Success" });
    //
    const results = await _moduleRepo.getBylistId(ids);
    return res.status(200).json({ data: results, code: 200, message: "Success" });
  } else {
    return res.send({ code: 404, message: "Missing ids" });
  }
});

//#region authorized
router.get("/getAll", async (req, res) => {
  const { pid, module } = req.headers;
  const _moduleRepo = new crudDA(`setting:${pid}:${module}`);
  const results = await _moduleRepo.getAll();
  return res.status(200).json({ data: results, code: 200, message: "Success" });
});

router.post("/getListSimple", async (req, res) => {
  const { pid, module } = req.headers;
  const _moduleRepo = new crudDA(`setting:${pid}:${module}`);
  const { searchRaw, page, size, returns, sortby } = req.body;
  const results = await _moduleRepo.search(page, size, searchRaw, { RETURN: returns, SORTBY: sortby });
  if (results.code !== 404) {
    return res.status(200).json({ data: results.data, totalCount: results.count, code: 200, message: "Success" });
  } else return res.send(results);
});

router.post("/action", async (req, res) => {
  const { pid, module, authorization } = req.headers;
  const payload = _Login.verifyToken((authorization ?? "").replace("Bearer", "").trim()) as JwtPayload;
  if (!payload?.id) return res.send({ code: 401, message: "Unauthorized" });
  const winiCustomerRoleRepo = new crudDA(`wini:ProjectCustomer`);
  const role = await winiCustomerRoleRepo.search(1, 1, `@CustomerId:{${payload.id}} @ProjectId:{${pid}} @Role:[${contructorDA.CustomerRole.owner} ${module !== "menu" ? contructorDA.CustomerRole.admin : contructorDA.CustomerRole.collaborator}]`);
  if (!role.data?.length) return res.send({ code: 403, message: "No permission." });
  const _moduleRepo = new crudDA(`setting:${pid}:${module}`);
  const { action } = req.query;
  const { data, ids } = req.body;

  switch (`${action}`.toLowerCase()) {
    case "add":
      try {
        await _moduleRepo.action("add", data);
        return res.status(200).json({ data: data, code: 200, message: "Success" });
      } catch (error) {
        return res.send({ code: 404, message: (error as any).message ?? "" });
      }
    case "edit":
      try {
        await _moduleRepo.action("edit", data);
        return res.status(200).json({ data: data, code: 200, message: "Success" });
      } catch (error) {
        return res.send({ code: 404, message: (error as any).message ?? "" });
      }
    case "delete":
      if (ids) {
        try {
          if (module === "table") {
            const _columnRepo = new crudDA(`setting:${pid}:column`);
            const _relRepo = new crudDA(`setting:${pid}:rel`);
            const _menuRepo = new crudDA(`setting:${pid}:menu`);
            const _chartRepo = new crudDA(`data:${pid}:data_chart`);
            const _cardRepo = new crudDA(`data:${pid}:data_card`);
            const _viewRepo = new crudDA(`data:${pid}:data_view`);
            const _formRepo = new crudDA(`data:${pid}:data_form`);
            const listTb: Array<any> = await _moduleRepo.getBylistId(ids);
            for (const tbn of listTb) {
              if (tbn) {
                const _dataRepo = new crudDA(`data:${pid}:${tbn.Name}`);
                _columnRepo.search(1, 500, `@TableName:{${tbn.Name}}`, { RETURN: "Id" }).then((listCol) => {
                  if (listCol.data) _columnRepo.delete(listCol.data.map((item: any) => item.Id));
                });
                _relRepo.search(1, 500, `(@TableFK:{${tbn.Name}}) | (@TablePK:{${tbn.Name}})`, { RETURN: "Id" }).then((listRel) => {
                  if (listRel.data) _relRepo.delete(listRel.data.map((item: any) => item.Id));
                });
                _menuRepo.search(1, 500, `@TableId:{${tbn.Id}}`, { RETURN: "Id" }).then((listMenu) => {
                  if (listMenu.data) _menuRepo.delete(listMenu.data.map((item: any) => item.Id));
                });
                _chartRepo.search(1, 2000, `@TbName:{${tbn.Name}}`, { RETURN: "Id" }).then((listChart) => {
                  if (listChart.data) _chartRepo.delete(listChart.data.map((item: any) => item.Id));
                });
                _cardRepo.search(1, 2000, `@TbName:{${tbn.Name}}`, { RETURN: "Id" }).then((listCard) => {
                  if (listCard.data) _cardRepo.delete(listCard.data.map((item: any) => item.Id));
                });
                _viewRepo.search(1, 2000, `@TbName:{${tbn.Name}}`, { RETURN: "Id" }).then((listView) => {
                  if (listView.data) _viewRepo.delete(listView.data.map((item: any) => item.Id));
                });
                _formRepo.search(1, 2000, `@TbName:{${tbn.Name}}`, { RETURN: "Id" }).then((listForm) => {
                  if (listForm.data) _formRepo.delete(listForm.data.map((item: any) => item.Id));
                });
                _dataRepo.deleteAll();
              }
            }
          }
          await _moduleRepo.delete(ids);
          return res.status(200).json({ data: ids, code: 200, message: "Success" });
        } catch (error) {
          return res.send({ code: 404, message: (error as any).message ?? "" });
        }
      } else {
        return res.send({ code: 404, message: "Missing ids!" });
      }
    default:
      return res.send({ code: 404, message: "Invalid action" });
  }
});

router.post("/duplicate", async (req, res) => {
  const { pid, authorization } = req.headers;
  const payload = _Login.verifyToken((authorization ?? "").replace("Bearer", "").trim()) as JwtPayload;
  if (!payload?.id) return res.send({ code: 401, message: "Unauthorized" });
  const winiCustomerRoleRepo = new crudDA(`wini:ProjectCustomer`);
  const role = await winiCustomerRoleRepo.search(1, 1, `@CustomerId:{${payload.id}} @ProjectId:{${pid}} @Role:[0 1]`);
  if (!role.data?.length) return res.send({ code: 403, message: "No permission." });
  const _moduleRepo = new crudDA(`setting:${pid}:table`);
  const { id, newTbName } = req.body;
  const _columnRepo = new crudDA(`setting:${pid}:column`);
  const _relRepo = new crudDA(`setting:${pid}:rel`);
  const _chartRepo = new crudDA(`data:${pid}:data_chart`);
  const _cardRepo = new crudDA(`data:${pid}:data_card`);
  const _viewRepo = new crudDA(`data:${pid}:data_view`);
  const _formRepo = new crudDA(`data:${pid}:data_form`);
  const tbData: any = await _moduleRepo.getById(id);
  const newModule = { ...tbData, Id: randomGID(), Name: newTbName, DateCreated: Date.now(), Description: `${tbData.Name}(copy)` };
  await _moduleRepo.action("add", [newModule]);
  if (tbData) {
    _columnRepo.search(1, 500, `@TableName:{${tbData.Name}}`).then((listCol) => {
      if (listCol.data?.length) {
        _columnRepo.action(
          "add",
          listCol.data.map((item: any) => ({ ...item, Id: randomGID(), DateCreated: Date.now(), TableName: newTbName }))
        );
      }
    });
    _relRepo.search(1, 500, `@TableFK:{${tbData.Name}}`).then((listRel) => {
      if (listRel.data?.length) {
        _relRepo.action(
          "add",
          listRel.data.map((item: any) => ({ ...item, Id: randomGID(), DateCreated: Date.now(), TableFK: newTbName }))
        );
      }
    });
    _chartRepo.search(1, 2000, `@TbName:{${tbData.Name}}`).then((listChart) => {
      if (listChart.data?.length) {
        _chartRepo.action(
          "add",
          listChart.data.map((item: any) => ({ ...item, Id: randomGID(), DateCreated: Date.now(), TbName: newTbName }))
        );
      }
    });
    _cardRepo.search(1, 2000, `@TbName:{${tbData.Name}}`).then((listCard) => {
      if (listCard.data?.length) {
        _cardRepo.action(
          "add",
          listCard.data.map((item: any) => ({ ...item, Id: randomGID(), DateCreated: Date.now(), TbName: newTbName, IsPublic: false }))
        );
      }
    });
    _viewRepo.search(1, 2000, `@TbName:{${tbData.Name}}`).then((listView) => {
      if (listView.data?.length) {
        _viewRepo.action(
          "add",
          listView.data.map((item: any) => ({ ...item, Id: randomGID(), DateCreated: Date.now(), TbName: newTbName, IsPublic: false }))
        );
      }
    });
    _formRepo.search(1, 2000, `@TbName:{${tbData.Name}}`).then((listForm) => {
      if (listForm.data?.length) {
        _formRepo.action(
          "add",
          listForm.data.map((item: any) => ({ ...item, Id: randomGID(), DateCreated: Date.now(), TbName: newTbName }))
        );
      }
    });
  }
  return res.status(200).json({ data: newModule, code: 200, message: "Success" });
});

router.get("/getProperties", async (req, res) => {
  const { pid } = req.headers;
  const { name } = req.query;
  const _tableRepo = new tableDA(`data:${pid}:${name}`);
  const result = await _tableRepo.getInterface();
  return res.status(200).json({ data: result, code: 200, message: "Success" });
});

router.post("/group", async (req, res) => {
  const { pid, module } = req.headers;
  const _moduleRepo = new crudDA(`setting:${pid}:${module}`);
  const { reducers, searchRaw } = req.body;
  const results = await _moduleRepo.aggregate({
    searchRaw: searchRaw,
    query: reducers,
  });
  if (results?.code !== 404) {
    const _totalCount = results.shift();
    if (_totalCount) {
      const _data = results.map((e: Array<any>) => {
        let _jsonItem: { [p: string]: any } = {};
        for (let i = 0; i < e.length; i += 2) {
          if (e[i] === "$") {
            let _parseJson = {};
            try {
              _parseJson = JSON.parse(e[i + 1]);
            } catch (error) {
              console.log("parse $ failed");
            }
            _jsonItem = { ..._jsonItem, ..._parseJson };
          } else {
            _jsonItem[e[i]] = e[i + 1];
          }
        }
        return _jsonItem;
      });
      return res.status(200).json({ data: _data, totalCount: _totalCount, code: 200, message: "Success" });
    } else {
      return res.status(200).json({ data: [], totalCount: 0, code: 200, message: "Success" });
    }
  } else {
    return res.send({ code: 404, message: results.message ?? "group error" });
  }
});

export default router;
