import { randomGID } from "../Ultis/convert";
import intergrationDA, { NotiType } from "../da/integrationDA";
import crudDA from "../da/crudDA";
import login from "../function/login";
import { JwtPayload } from "jsonwebtoken";
const _Login = new login();

interface Props {
  pid: string;
  module: string;
  data: Array<any>;
  ids: Array<string>;
  action: string;
  authorization: string;
}
const adminKtxId = "348ad5b7508e45c4886d5d2031767bf9";
const ktxCompanyId = "08cef75a7b9c4db7a2831504f47dd4fe";

enum RoleType {
  admin = 1,
  subadmin = 2,
  member = 3,
}

enum StatusRole {
  invited = 0,
  requested = 1,
  joined = 2,
}

const regexGetMentionCustomer = /href="[^"]*\/profile-social\?id=([a-fA-F0-9]{32})"/g;
const regexGetGuid = /[a-fA-F0-9]{32}/;

export async function ktxNotifications({ pid, module, data, ids, action, authorization }: Props) {
  if (pid !== "944b6a5577524e1eadef54ab63598daa") return;
  const _moduleRepo = new crudDA(`data:${pid}:${module}`);
  const notificationRepo = new crudDA(`data:${pid}:Notification`);
  const customerIds: Array<string> = [];
  let payload: JwtPayload | undefined;
  try {
    if (authorization) payload = _Login.verifyToken((authorization ?? "").replace("Bearer", "").trim()) as JwtPayload;
  } catch (error) {}
  switch (`${action}`.toLowerCase()) {
    case "add":
      switch (module) {
        case "ToiletServices":
          customerIds.push(...data.map((e: any) => e.CustomerId).filter((e?: string) => e?.length));
          if (customerIds.length) {
            const customerRepo = new crudDA(`data:${pid}:Customer`);
            const res = (await customerRepo.getBylistId(customerIds)) as Array<any>;
            const customers = [...res];
            if (customers[0].CompanyProfileId) {
              const companyRepo = new crudDA(`data:${pid}:CustomerCompany`);
              const members = (await companyRepo.search(1, 1000, `@CompanyProfileId:{${customers[0].CompanyProfileId}} @Status:[1 1] @Role:(*Coordinator*)`, { RETURN: ["Id", "CustomerId"] })) as any;
              if (members.data?.length) {
                const memberIds = members.data.map((e: any) => e.CustomerId);
                const memberCustomer = await customerRepo.getBylistId(memberIds);
                customers.push(...memberCustomer);
              }
            }
            const deviceTokens = customers
              .filter((e: any) => e.DeviceToken)
              .map((e: any) => e.DeviceToken.split(","))
              .flat(Infinity)
              .filter((devToken, i, arr) => devToken.length && arr.indexOf(devToken) === i);
            if (deviceTokens.length) {
              const intergration = new intergrationDA(pid);
              await intergration.sendMessageToGroup({
                noti: { title: "Đơn hàng mới", body: `${customers[0].CompanyProfileId ? "Doanh nghiệp" : "Bạn"} có đơn hàng mới` },
                data: { id: randomGID(), type: NotiType.changeToiletServiceStatus, url: `workspace?id=${data[0].Id}` },
                deviceTokens: deviceTokens,
              });
            }
            notificationRepo.action(
              "add",
              customers.map((e: any) => {
                return {
                  Id: randomGID(),
                  Name: "Đơn hàng mới",
                  DateCreated: Date.now(),
                  Sort: 1,
                  Content: `${customers[0].CompanyProfileId ? "Doanh nghiệp" : "Bạn"} có đơn hàng mới`,
                  Status: 0,
                  Type: NotiType.changeToiletServiceStatus,
                  ToiletServicesId: data[0].Id,
                  CustomerId: e.Id,
                  LinkWeb: `/workspace?id=${data[0].Id}`,
                  LinkApp: `DetailWorkView`,
                };
              })
            );
          }
          break;
        case "CustomerCompany":
          customerIds.push(...data.map((e: any) => e.CustomerId).filter((e?: string) => e?.length));
          if (customerIds.length) {
            const companyRepo = new crudDA(`data:${pid}:CompanyProfile`);
            const company = (await companyRepo.getById(data[0].CompanyProfileId)) as any;
            const customerRepo = new crudDA(`data:${pid}:Customer`);
            const res = (await customerRepo.getBylistId(customerIds)) as Array<any>;
            const customers = [...res];
            const deviceTokens = customers
              .filter((e: any) => e.DeviceToken)
              .map((e: any) => e.DeviceToken.split(","))
              .flat(Infinity)
              .filter((devToken, i, arr) => devToken.length && arr.indexOf(devToken) === i);
            if (deviceTokens.length) {
              const intergration = new intergrationDA(pid as string);
              await intergration.sendMessageToGroup({
                noti: { title: "Bạn có lời mời vào doanh nghiệp", body: `Bạn được ${company.Name} mời vào hệ thống. Vui lòng truy cập hệ thống và xác nhận.` },
                data: { id: randomGID(), type: NotiType.invite, url: "home" },
                deviceTokens: deviceTokens,
              });
            }
            notificationRepo.action(
              "add",
              customers.map((e: any) => {
                return {
                  Id: randomGID(),
                  Name: "Bạn có lời mời vào doanh nghiệp",
                  DateCreated: Date.now(),
                  Sort: 1,
                  Content: `Bạn được ${company.Name} mời vào hệ thống. Vui lòng truy cập hệ thống và xác nhận.`,
                  Status: 0,
                  Type: NotiType.invite,
                  CustomerCompanyId: data[0].Id,
                  CustomerId: e.Id,
                  LinkApp: "CompanyView",
                };
              })
            );
          }
          break;
        case "Ticket":
          if (data[0].ToiletId) {
            const toiletRepo = new crudDA(`data:${pid}:Toilet`);
            const toiletData = (await toiletRepo.getById(data[0].ToiletId)) as any;
            customerIds.push(toiletData.CustomerId);
          } else if (data[0].ToiletServicesId) {
            const toiletServicesRepo = new crudDA(`data:${pid}:ToiletServices`);
            const toiletServicesData = (await toiletServicesRepo.getById(data[0].ToiletServicesId)) as any;
            customerIds.push(toiletServicesData.CustomerId);
          } else {
            customerIds.push(adminKtxId);
          }
          if (customerIds.length) {
            const customerRepo = new crudDA(`data:${pid}:Customer`);
            const res = (await customerRepo.getBylistId(customerIds)) as Array<any>;
            const customers = [...res];
            if (customers[0].CompanyProfileId) {
              const companyRepo = new crudDA(`data:${pid}:CustomerCompany`);
              const members = (await companyRepo.search(1, 1000, `@CompanyProfileId:{${customers[0].CompanyProfileId}} @Status:[1 1] @Role:(*Coordinator*)`, { RETURN: ["Id", "CustomerId"] })) as any;
              if (members.data?.length) {
                const memberIds = members.data.map((e: any) => e.CustomerId);
                const memberCustomer = await customerRepo.getBylistId(memberIds);
                customers.push(...memberCustomer);
              }
            }
            const deviceTokens = customers
              .filter((e: any) => e.DeviceToken)
              .map((e: any) => e.DeviceToken.split(","))
              .flat(Infinity)
              .filter((devToken, i, arr) => devToken.length && arr.indexOf(devToken) === i);
            if (deviceTokens.length) {
              const intergration = new intergrationDA(pid);
              intergration.sendMessageToGroup({
                noti: { title: "Có ticket mới cần bạn xử lý", body: `Ticket ${data[0].Name} cần bạn xử lý` },
                data: { id: randomGID(), type: NotiType.changeTicketStatus, url: "manager/ticket" },
                deviceTokens: deviceTokens,
              });
            }
            notificationRepo.action(
              "add",
              customers.map((e: any) => {
                return {
                  Id: randomGID(),
                  Name: "Có ticket mới cần bạn xử lý",
                  DateCreated: Date.now(),
                  Sort: 1,
                  Content: `Ticket ${data[0].Name} cần bạn xử lý`,
                  Status: 0,
                  Type: NotiType.changeTicketStatus,
                  CustomerId: e.Id,
                  LinkWeb: `/manager/ticket`,
                  LinkApp: "MyTicketList",
                };
              })
            );
          }
          break;
        case "Survey":
          if (data[0].ToiletServicesId) {
            const toiletServicesRepo = new crudDA(`data:${pid}:ToiletServices`);
            toiletServicesRepo.getById(data[0].ToiletServicesId).then(async (toiletServices: any) => {
              if (toiletServices) {
                const customerRepo = new crudDA(`data:${pid}:Customer`);
                const customer = await customerRepo.search(1, 1, `@Mobile:("*${toiletServices.CustomerMobile}*")`);
                if (customer.data?.[0]) {
                  const customers = customer.data;
                  const deviceTokens = customers
                    .filter((e: any) => e.DeviceToken)
                    .map((e: any) => e.DeviceToken.split(","))
                    .flat(Infinity)
                    .filter((devToken, i, arr) => devToken.length && arr.indexOf(devToken) === i);
                  if (deviceTokens.length) {
                    const intergration = new intergrationDA(pid);
                    await intergration.sendMessageToGroup({
                      noti: { title: `Đối tác đã hoàn thành và gửi thông khảo sát đơn hàng ${toiletServices.Name}`, body: `Đối tác đã hoàn thành và gửi thông tin khảo sát đơn hàng ${toiletServices.Name}. Hãy xác nhận các thông tin trên để đơn hàng được chuyển sang bước tư vấn báo giá.` },
                      data: { id: randomGID(), type: NotiType.changeToiletServiceStatus, url: `workspace?id=${data[0].ToiletServicesId}` },
                      deviceTokens: deviceTokens,
                    });
                  }
                  notificationRepo.action(
                    "add",
                    customers.map((e: any) => {
                      return {
                        Id: randomGID(),
                        Name: `Đối tác đã hoàn thành và gửi thông khảo sát đơn hàng ${toiletServices.Name}`,
                        DateCreated: Date.now(),
                        Sort: 1,
                        Content: `Đối tác đã hoàn thành và gửi thông tin khảo sát đơn hàng ${toiletServices.Name}. Hãy xác nhận các thông tin trên để đơn hàng được chuyển sang bước tư vấn báo giá.`,
                        Status: 0,
                        Type: NotiType.changeToiletServiceStatus,
                        ToiletServicesId: data[0].ToiletServicesId,
                        CustomerId: e.Id,
                        LinkWeb: `/workspace?id=${data[0].ToiletServicesId}`,
                        LinkApp: `DetailWorkView`,
                      };
                    })
                  );
                }
              }
            });
          }
          break;
        // social
        case "Posts":
          if (data[0].GroupId) {
            const groupRepo = new crudDA(`data:${pid}:Group`);
            groupRepo.getById(data[0].GroupId).then(async (group: any) => {
              if (group) {
                const roleInGroup = new crudDA(`data:${pid}:Role`);
                const members = (await roleInGroup.search(1, 10000, `(-CustomerId:{${data[0].CustomerId}}) @GroupId:{${data[0].GroupId}} @Status:[${StatusRole.joined}]`, { RETURN: ["Id", "CustomerId"] })) as any;
                if (members.data?.length) {
                  const memberIds = members.data.map((e: any) => e.CustomerId);
                  customerIds.push(...memberIds);
                }
                if (customerIds.length) {
                  const customerRepo = new crudDA(`data:${pid}:Customer`);
                  let customers = await customerRepo.getBylistId([data[0].CustomerId, ...customerIds]);
                  const postOwner = customers.shift() as any;
                  customers = customers.filter((e: any) => e !== undefined && e !== null);
                  const deviceTokens = customers
                    .filter((e: any) => e.DeviceToken)
                    .map((e: any) => e.DeviceToken.split(","))
                    .flat(Infinity)
                    .filter((devToken, i, arr) => devToken.length && arr.indexOf(devToken) === i);
                  if (deviceTokens.length) {
                    const intergration = new intergrationDA(pid as string);
                    await intergration.sendMessageToGroup({
                      noti: { title: `${postOwner.Name} đã đăng bài viết mới`, body: `${postOwner.Name} đã đăng bài viết mới trong nhóm ${group.Name}` },
                      data: { id: randomGID(), type: NotiType.post, url: `post?id=${data[0].Id}` },
                      deviceTokens: deviceTokens,
                    });
                  }
                  notificationRepo.action(
                    "add",
                    customers.map((e: any) => {
                      return {
                        Id: randomGID(),
                        Name: `${postOwner.Name} đã đăng bài viết mới`,
                        DateCreated: Date.now(),
                        Sort: 1,
                        Content: `${postOwner.Name} đã đăng bài viết mới trong nhóm ${group.Name}`,
                        Status: 0,
                        Type: NotiType.post,
                        CustomerId: e.Id,
                        LinkWeb: `/post?id=${data[0].Id}`,
                        LinkApp: "PostDetail",
                      };
                    })
                  );
                }
              }
            });
          }
          const getPostMentions = data[0].Content.match(regexGetMentionCustomer);
          if (getPostMentions) {
            const customerIds = getPostMentions.map((e: string) => e.match(regexGetGuid)![0]);
            if (customerIds.length) {
              const customerRepo = new crudDA(`data:${pid}:Customer`);
              customerRepo.getBylistId([data[0].CustomerId, ...customerIds]).then(async (cusRes) => {
                let customers = [...cusRes];
                const postOwner = customers.shift() as any;
                customers = customers.filter((e: any) => e !== undefined && e !== null);
                const deviceTokens = customers
                  .filter((e: any) => e.DeviceToken)
                  .map((e: any) => e.DeviceToken.split(","))
                  .flat(Infinity);
                if (deviceTokens.length) {
                  const intergration = new intergrationDA(pid as string);
                  await intergration.sendMessageToGroup({
                    noti: { title: `${postOwner.Name} đã nhắc đến bạn trong bài viết mới`, body: `${postOwner.Name} đã nhăc đến bạn trong bài viết mới` },
                    data: { id: randomGID(), type: NotiType.mention, url: `post?id=${data[0].Id}` },
                    deviceTokens: deviceTokens,
                  });
                }
                notificationRepo.action(
                  "add",
                  customers.map((e: any) => {
                    return {
                      Id: randomGID(),
                      Name: `${postOwner.Name} đã nhắc đến bạn trong bài viết mới`,
                      DateCreated: Date.now(),
                      Sort: 1,
                      Content: `${postOwner.Name} đã nhắc đến bạn trong bài viết mới`,
                      Status: 0,
                      Type: NotiType.mention,
                      CustomerId: e.Id,
                      LinkWeb: `/post?id=${data[0].Id}`,
                      LinkApp: "PostDetail",
                    };
                  })
                );
              });
            }
          }
          break;
        case "Comments":
          var _repo: crudDA | undefined = undefined;
          if (data[0].PostsId) {
            _repo = new crudDA(`data:${pid}:Posts`);
          } else if (data[0].NewsId) {
            _repo = new crudDA(`data:${pid}:News`);
          }
          if (_repo) {
            _repo.getById(data[0].PostsId ?? data[0].NewsId).then(async (item: any) => {
              if (item) {
                const customerIds: Array<string> = [data[0].CustomerId, item.CustomerId];
                if (data[0].ParentId) {
                  const parentCmt = (await _moduleRepo.getById(data[0].ParentId)) as any;
                  customerIds.push(parentCmt.CustomerId);
                }
                const customerRepo = new crudDA(`data:${pid}:Customer`);
                const customers = (await customerRepo.getBylistId(customerIds)) as any;
                if (customers[1]) {
                  const deviceTokens = customers[1].DeviceToken?.split(",") ?? [];
                  if (deviceTokens.length) {
                    const intergration = new intergrationDA(pid as string);
                    await intergration.sendMessageToGroup({
                      noti: { title: `${customers[0].Name} đã bình luận về ${data[0].PostsId ? "bài viết" : "tin tức"} của bạn`, body: `${customers[0].Name} đã bình luận về ${data[0].PostsId ? "bài viết" : "tin tức"} của bạn` },
                      data: { id: randomGID(), type: NotiType.comment, url: data[0].PostsId ? `post?id=${data[0].Id}` : `news?id=${data[0].NewsId}` },
                      deviceTokens: deviceTokens,
                    });
                  }
                  notificationRepo.action("add", [
                    {
                      Id: randomGID(),
                      Name: `${customers[0].Name} đã bình luận về ${data[0].PostsId ? "bài viết" : "tin tức"} của bạn`,
                      DateCreated: Date.now(),
                      Sort: 1,
                      Content: `${customers[0].Name} đã bình luận về ${data[0].PostsId ? "bài viết" : "tin tức"} của bạn`,
                      Status: 0,
                      Type: NotiType.comment,
                      CustomerId: customers[1].Id,
                      LinkWeb: data[0].PostsId ? `/post?id=${data[0].PostsId}` : `/news?id=${data[0].NewsId}`,
                      LinkApp: "PostDetail",
                    },
                  ]);
                } else if (customers[2]) {
                  const deviceTokens = customers[2].DeviceToken?.split(",") ?? [];
                  if (deviceTokens.length) {
                    const intergration = new intergrationDA(pid as string);
                    await intergration.sendMessageToGroup({
                      noti: { title: `${customers[0].Name} đã trả lời về bình luận của bạn`, body: `${customers[0].Name} đã trả lời về bình luận của bạn` },
                      data: { id: randomGID(), type: NotiType.comment, url: data[0].PostsId ? `post?id=${data[0].Id}` : `news?id=${data[0].NewsId}` },
                      deviceTokens: deviceTokens,
                    });
                  }
                  notificationRepo.action("add", [
                    {
                      Id: randomGID(),
                      Name: `${customers[0].Name} đã trả lời về bình luận của bạn`,
                      DateCreated: Date.now(),
                      Sort: 1,
                      Content: `${customers[0].Name} đã trả lời về bình luận của bạn`,
                      Status: 0,
                      Type: NotiType.comment,
                      CustomerId: customers[2].Id,
                      LinkWeb: data[0].PostsId ? `/post?id=${data[0].PostsId}` : `/news?id=${data[0].NewsId}`,
                      LinkApp: "PostDetail",
                    },
                  ]);
                }
              }
            });
          }
          const getCmtMentions = data[0].Content.match(regexGetMentionCustomer);
          if (getCmtMentions) {
            const customerIds = getCmtMentions.map((e: string) => e.match(regexGetGuid)![0]);
            if (customerIds.length) {
              const customerRepo = new crudDA(`data:${pid}:Customer`);
              customerRepo.getBylistId([data[0].CustomerId, ...customerIds]).then(async (cusRes) => {
                let customers = [...cusRes];
                const cmtOwner = customers.shift() as any;
                customers = customers.filter((e: any) => e !== undefined && e !== null);
                const deviceTokens = customers
                  .filter((e: any) => e.DeviceToken)
                  .map((e: any) => e.DeviceToken.split(","))
                  .flat(Infinity);
                if (deviceTokens.length) {
                  const intergration = new intergrationDA(pid as string);
                  await intergration.sendMessageToGroup({
                    noti: { title: `${cmtOwner.Name} đã nhắc đến bạn trong 1 bình luận`, body: `${cmtOwner.Name} đã nhăc đến bạn trong 1 bình luận` },
                    data: { id: randomGID(), type: NotiType.mention, url: data[0].PostsId ? `post?id=${data[0].Id}` : `news?id=${data[0].NewsId}` },
                    deviceTokens: deviceTokens,
                  });
                }
                notificationRepo.action(
                  "add",
                  customers.map((e: any) => {
                    return {
                      Id: randomGID(),
                      Name: `${cmtOwner.Name} đã nhắc đến bạn trong 1 bình luận`,
                      DateCreated: Date.now(),
                      Sort: 1,
                      Content: `${cmtOwner.Name} đã nhắc đến bạn trong 1 bình luận`,
                      Status: 0,
                      Type: NotiType.mention,
                      CustomerId: e.Id,
                      LinkWeb: data[0].PostsId ? `/post?id=${data[0].PostsId}` : `/news?id=${data[0].NewsId}`,
                      LinkApp: "PostDetail",
                    };
                  })
                );
              });
            }
          }
          break;
        case "Likes":
          var _repo: crudDA | undefined = undefined;
          if (data[0].PostsId) {
            _repo = new crudDA(`data:${pid}:Posts`);
          } else if (data[0].NewsId) {
            _repo = new crudDA(`data:${pid}:News`);
          } else if (data[0].CommentsId) {
            _repo = new crudDA(`data:${pid}:Comments`);
          }
          if (_repo) {
            _repo.getById(data[0].PostsId ?? data[0].NewsId ?? data[0].CommentsId).then(async (item: any) => {
              if (item) {
                const customerIds: Array<string> = [data[0].CustomerId, item.CustomerId];
                const customerRepo = new crudDA(`data:${pid}:Customer`);
                const customers = (await customerRepo.getBylistId(customerIds)) as any;
                if (customers[1]) {
                  const navLink = data[0].PostsId ? `post?id=${data[0].PostsId}` : data[0].NewsId ? `news?id=${data[0].NewsId}` : item.PostsId ? `post?id=${item.PostsId}` : `news?id=${item.NewsId}`;
                  const deviceTokens = customers[1].DeviceToken?.split(",") ?? [];
                  if (deviceTokens.length) {
                    const intergration = new intergrationDA(pid as string);
                    await intergration.sendMessageToGroup({
                      noti: { title: `${customers[0].Name} đã thích ${data[0].PostsId ? "bài viết" : data[0].NewsId ? "tin tức" : "bình luận"} của bạn`, body: `${customers[0].Name} đã thích ${data[0].PostsId ? "bài viết" : data[0].NewsId ? "tin tức" : "bình luận"} của bạn` },
                      data: { id: randomGID(), type: NotiType.like, url: navLink },
                      deviceTokens: deviceTokens,
                    });
                  }
                  notificationRepo.action("add", [
                    {
                      Id: randomGID(),
                      Name: `${customers[0].Name} đã thích ${data[0].PostsId ? "bài viết" : data[0].NewsId ? "tin tức" : "bình luận"} của bạn`,
                      DateCreated: Date.now(),
                      Sort: 1,
                      Content: `${customers[0].Name} đã thích ${data[0].PostsId ? "bài viết" : data[0].NewsId ? "tin tức" : "bình luận"} của bạn`,
                      Status: 0,
                      Type: NotiType.like,
                      CustomerId: customers[1].Id,
                      LinkWeb: "/" + navLink,
                      LinkApp: "PostDetail",
                    },
                  ]);
                }
              }
            });
          }
          break;
        case "Role": // role in group
          if (data[0].Status === StatusRole.invited && data[0].GroupId) {
            customerIds.push(payload!.id, ...data.map((e) => e.CustomerId));
            if (customerIds.length > 1) {
              const customerRepo = new crudDA(`data:${pid}:Customer`);
              customerRepo.getBylistId(customerIds).then(async (res: any) => {
                const customers = [...res];
                const groupRepo = new crudDA(`data:${pid}:Group`);
                const group = (await groupRepo.getById(data[0].GroupId)) as any;
                const deviceTokens = customers
                  .slice(1)
                  .filter((e: any) => e.DeviceToken)
                  .map((e: any) => e.DeviceToken.split(","))
                  .flat(Infinity);
                if (deviceTokens.length) {
                  const intergration = new intergrationDA(pid as string);
                  await intergration.sendMessageToGroup({
                    noti: { title: `${customers[0].Name} đã mời bạn tham gia nhóm ${group.Name}`, body: `${customers[0].Name} đã mời bạn tham gia nhóm ${group.Name}` },
                    data: { id: randomGID(), type: NotiType.invite, url: `groups/d?id=${group.Id}` },
                    deviceTokens: deviceTokens,
                  });
                }
                notificationRepo.action(
                  "add",
                  customers.slice(1).map((e: any) => {
                    return {
                      Id: randomGID(),
                      Name: `${customers[0].Name} đã mời bạn tham gia nhóm ${group.Name}`,
                      DateCreated: Date.now(),
                      Sort: 1,
                      Content: `${customers[0].Name} đã mời bạn tham gia nhóm ${group.Name}`,
                      Status: 0,
                      Type: NotiType.invite,
                      CustomerId: e.Id,
                      LinkWeb: `/groups/d?id=${group.Id}`,
                      LinkApp: "GroupIndex",
                    };
                  })
                );
              });
            }
          } else if (data[0].Status === StatusRole.requested && data[0].GroupId) {
            const groupRepo = new crudDA(`data:${pid}:Group`);
            groupRepo.getById(data[0].GroupId).then(async (group: any) => {
              if (group) {
                const roleInGroup = new crudDA(`data:${pid}:Role`);
                const members = (await roleInGroup.search(1, 10000, `@GroupId:{${data[0].GroupId}} @Status:[${StatusRole.joined}] @Type:[${RoleType.admin} ${RoleType.subadmin}]`, { RETURN: ["Id", "CustomerId"] })) as any;
                if (members.data?.length) {
                  const memberIds = members.data.map((e: any) => e.CustomerId);
                  customerIds.push(...memberIds);
                }
                if (customerIds.length) {
                  const customerRepo = new crudDA(`data:${pid}:Customer`);
                  let customers = await customerRepo.getBylistId([data[0].CustomerId, ...customerIds]);
                  const requestOwner = customers.shift() as any;
                  customers = customers.filter((e: any) => e !== undefined && e !== null);
                  const deviceTokens = customers
                    .filter((e: any) => e.DeviceToken)
                    .map((e: any) => e.DeviceToken.split(","))
                    .flat(Infinity)
                    .filter((devToken, i, arr) => devToken.length && arr.indexOf(devToken) === i);
                  if (deviceTokens.length) {
                    const intergration = new intergrationDA(pid as string);
                    await intergration.sendMessageToGroup({
                      noti: { title: `${requestOwner.Name} đã yêu cầu tham gia nhóm ${group.Name}`, body: `${requestOwner.Name} đã yêu cầu tham gia nhóm ${group.Name}` },
                      data: { id: randomGID(), type: NotiType.request, url: `groups/d?id=${group.Id}` },
                      deviceTokens: deviceTokens,
                    });
                  }
                  notificationRepo.action(
                    "add",
                    customers.map((e: any) => {
                      return {
                        Id: randomGID(),
                        Name: `${requestOwner.Name} đã yêu cầu tham gia nhóm ${group.Name}`,
                        DateCreated: Date.now(),
                        Sort: 1,
                        Content: `${requestOwner.Name} đã yêu cầu tham gia nhóm ${group.Name}`,
                        Status: 0,
                        Type: NotiType.request,
                        CustomerId: e.Id,
                        LinkWeb: `/groups/d?id=${group.Id}`,
                        LinkApp: "GroupIndex",
                      };
                    })
                  );
                }
              }
            });
          }
          break;
        default:
          break;
      }
      break;
    case "edit":
      switch (module) {
        case "ToiletServices":
          const currentToiletServices = (await _moduleRepo.getById(data[0].Id)) as any;
          if (currentToiletServices?.Status < data[0].Status || currentToiletServices?.Status === 8) {
            var title: undefined | string = undefined;
            var notiBody: undefined | string = undefined;
            switch (data[0].Status) {
              case 2:
                title = `Đơn hàng ${data[0].Name} đã được tiếp nhận.`;
                notiBody = `Đơn hàng ${data[0].Name} đã được tiếp nhận và đang được đối tác khảo sát.`;
                break;
              case 3:
                title = `Đơn hàng ${data[0].Name} đã hoàn thành khảo sát.`;
                notiBody = `Đơn hàng ${data[0].Name} đã hoàn thành khảo sát và chuyển sang sang bước tư vấn báo giá.`;
                break;
              case 4:
                break;
              case 5:
                title = `Đơn hàng ${data[0].Name} đã chốt báo giá.`;
                notiBody = `Đơn hàng ${data[0].Name} đã chốt báo giá và chuyển sang bước ký kết hợp đồng.`;
                break;
              case 6:
                break;
              case 7:
                title = currentToiletServices?.Status === 8 ? `Khách hàng đã từ chối bản thiết kế đơn hàng ${data[0].Name}.` : `Đơn hàng ${data[0].Name} đã ký hợp đồng.`;
                notiBody = currentToiletServices?.Status === 8 ? `Khách hàng đã từ chối bản thiết kế đơn hàng ${data[0].Name}. Hãy cập nhật các phiên bản thiết kế mới bạn nhé.` : `Đơn hàng ${data[0].Name} đã ký hợp đồng và chuyển sang bước thiết kế.`;
                break;
              case 8:
                title = `Đối tác ${data[0].Name} đã gửi các tệp thiết kế.`;
                notiBody = `Đơn hàng ${data[0].Name} đã ký hợp đồng và chuyển sang bước thiết kế.`;
                break;
              case 9:
                title = `Đơn hàng ${data[0].Name} đã ${currentToiletServices.Status === 7 ? "ký hợp đồng" : "chốt thiết kế"}.`;
                notiBody = `Đơn hàng ${data[0].Name} đã ${currentToiletServices.Status === 7 ? "ký hợp đồng" : "chốt thiết kế"} và chuyển sang bước thực hiện hợp đồng.`;
                break;
              case 10:
                break;
              case 11:
                title = `Đơn hàng ${data[0].Name} đã ký biên bản nghiệm thu.`;
                notiBody = `Đơn hàng ${data[0].Name} đã ký biên bản nghiệm thu và chuyển sang bước thanh lý hợp đồng.`;
                break;
              case 12:
                title = `Đơn hàng ${data[0].Name} đã được thanh lý hợp đồng.`;
                notiBody = title;
                break;
              default:
                break;
            }
            if (title && notiBody) {
              const customerRepo = new crudDA(`data:${pid}:Customer`);
              customerRepo.search(1, 1, `${currentToiletServices?.Status === 8 ? `@Id:{${data[0].CustomerId}}` : `@Mobile:("*${data[0].CustomerMobile}*")`}`).then(async (res: any) => {
                if (!res.data[0]) return;
                const customers = [...res.data];
                if (customers[0].CompanyProfileId && currentToiletServices?.Status === 8) {
                  const companyRepo = new crudDA(`data:${pid}:CustomerCompany`);
                  const members = (await companyRepo.search(1, 1000, `@CompanyProfileId:{${customers[0].CompanyProfileId}} @Status:[1 1] @Role:(*Coordinator*)`, { RETURN: ["Id", "CustomerId"] })) as any;
                  if (members.data?.length) {
                    const memberIds = members.data.map((e: any) => e.CustomerId);
                    const memberCustomer = await customerRepo.getBylistId(memberIds);
                    customers.push(...memberCustomer);
                  }
                }
                const deviceTokens = customers
                  .filter((e: any) => e.DeviceToken)
                  .map((e: any) => e.DeviceToken.split(","))
                  .flat(Infinity)
                  .filter((devToken, i, arr) => devToken.length && arr.indexOf(devToken) === i);
                if (deviceTokens.length) {
                  const intergration = new intergrationDA(pid as string);
                  await intergration.sendMessageToGroup({
                    noti: { title: title ?? "", body: notiBody ?? "" },
                    data: { id: randomGID(), type: NotiType.changeToiletServiceStatus, url: `workspace?id=${data[0].Id}` },
                    deviceTokens: deviceTokens,
                  });
                }
                notificationRepo.action(
                  "add",
                  customers.map((e: any) => {
                    return {
                      Id: randomGID(),
                      Name: title,
                      DateCreated: Date.now(),
                      Sort: 1,
                      Content: notiBody,
                      Status: 0,
                      Type: NotiType.changeToiletServiceStatus,
                      ToiletServicesId: data[0].Id,
                      CustomerId: e.Id,
                      LinkWeb: `/workspace?id=${data[0].Id}`,
                      LinkApp: "DetailWorkView",
                    };
                  })
                );
              });
            }
          }
          break;
        case "CustomerCompany":
          _moduleRepo.search(1, 1000, `@CompanyProfileId:{${data[0].CompanyProfileId}} @Status:[1 1] @Role:(*Coordinator* | *Owner*)`, { RETURN: ["Id", "CustomerId"] }).then(async (members: any) => {
            const customerRepo = new crudDA(`data:${pid}:Customer`);
            if (members.data?.length) {
              const memberIds = members.data.map((e: any) => e.CustomerId);
              const memberCustomer = await customerRepo.getBylistId(memberIds);
              const deviceTokens = memberCustomer
                .filter((e: any) => e.DeviceToken)
                .map((e: any) => e.DeviceToken.split(","))
                .flat(Infinity)
                .filter((devToken, i, arr) => devToken.length && arr.indexOf(devToken) === i);
              const customerData = (await customerRepo.getById(data[0].CustomerId)) as any;
              if (deviceTokens.length) {
                const intergration = new intergrationDA(pid as string);
                intergration.sendMessageToGroup({
                  noti: { title: customerData.Name + " đã chấp nhận tham gia doanh nghiệp của bạn", body: customerData.Name + " đã trở thành thành viên doanh nghiệp của bạn" },
                  data: { id: randomGID(), type: NotiType.acceptInvite, url: `setting/company` },
                  deviceTokens: deviceTokens,
                });
              }
              notificationRepo.action(
                "add",
                memberCustomer.map((e: any) => {
                  return {
                    Id: randomGID(),
                    Name: customerData.Name + " đã chấp nhận tham gia doanh nghiệp của bạn",
                    DateCreated: Date.now(),
                    Sort: 1,
                    Content: customerData.Name + " đã trở thành thành viên doanh nghiệp của bạn",
                    Status: 0,
                    Type: NotiType.acceptInvite,
                    CustomerCompanyId: data[0].Id,
                    CustomerId: e.Id,
                    LinkWeb: `/setting/company`,
                    LinkApp: "CompanyView",
                  };
                })
              );
            }
          });
          break;
        case "Task":
          if (!data[0].Price) {
            const taskRepo = new crudDA(`data:${pid}:Task`);
            await taskRepo.getById(data[0].Id).then(async (currentTask: any) => {
              if (currentTask.Status !== data[0].Status || currentTask.CustomerId !== data[0].CustomerId) {
                switch (data[0].Type) {
                  case 1:
                  //   case 2:
                  //   case 3:
                  //   case 4:
                  //   case 5:
                  case 6:
                    const customerRepo = new crudDA(`data:${pid}:Customer`);
                    const customers: Array<any> = [];
                    if (data[0].ToiletServicesId) {
                      const toiletServicesRepo = new crudDA(`data:${pid}:ToiletServices`);
                      const toiletService = (await toiletServicesRepo.getById(data[0].ToiletServicesId)) as any;
                      customerIds.push(toiletService.CustomerId);
                      const partner = (await customerRepo.getById(toiletService.CustomerId)) as any;
                      if (partner) {
                        customers.push(partner);
                        if (partner.CompanyProfileId) {
                          const customerCompanyRepo = new crudDA(`data:${pid}:CustomerCompany`);
                          const findOwnerCoodinator = await customerCompanyRepo.search(1, 1000, `@CompanyProfileId:{${partner.CompanyProfileId}} @Role:(*Coordinator* | *Owner*) -@CustomerId:{${partner.Id}}`, { RETURN: ["Id", "CustomerId"] });
                          if (findOwnerCoodinator.count) customerIds.push(...findOwnerCoodinator.data.map((e: any) => e.CustomerId));
                        }
                      }
                    } else if (data[0].ToiletId) {
                      const toiletRepo = new crudDA(`data:${pid}:Toilet`);
                      const toilet = (await toiletRepo.getById(data[0].ToiletId)) as any;
                      customerIds.push(toilet.CustomerId);
                      const toiletOwner = (await customerRepo.getById(toilet.CustomerId)) as any;
                      if (toiletOwner) {
                        customers.push(toiletOwner);
                        if (toiletOwner.CompanyProfileId) {
                          const customerCompanyRepo = new crudDA(`data:${pid}:CustomerCompany`);
                          const findOwnerCoodinator = await customerCompanyRepo.search(1, 1000, `@CompanyProfileId:{${toiletOwner.CompanyProfileId}} @Role:(*Coordinator* | *Owner*) -@CustomerId:{${toiletOwner.Id}}`, { RETURN: ["Id", "CustomerId"] });
                          if (findOwnerCoodinator.count) customerIds.push(...findOwnerCoodinator.data.map((e: any) => e.CustomerId));
                        }
                      }
                    }
                    const customerList = await customerRepo.getBylistId(customerIds);
                    customers.push(...customerList);
                    const deviceTokens = customers
                      .filter((e: any) => e.DeviceToken)
                      .map((e: any) => e.DeviceToken.split(","))
                      .flat(Infinity)
                      .filter((devToken, i, arr) => devToken.length && arr.indexOf(devToken) === i);
                    if (currentTask.Status !== data[0].Status) {
                      switch (data[0].Status) {
                        case 1:
                          var title = `Công việc ${data[0].Name} đã chuyển sang trạng thái mở`;
                          break;
                        case 2:
                          title = `Công việc ${data[0].Name} đang được thực hiện`;
                          break;
                        case 3:
                          title = `Công việc ${data[0].Name} đã quá hạn`;
                          break;
                        case 4:
                          title = `Công việc ${data[0].Name} đã hoàn thành`;
                          break;
                        case 5:
                          title = `Công việc ${data[0].Name} đã chuyển sang trạng thái đóng`;
                          break;
                        default:
                          title = "";
                          break;
                      }
                    } else {
                      title = "Công việc " + data[0].Name + "đã đổi người thực hiện";
                    }
                    if (deviceTokens.length) {
                      const intergration = new intergrationDA(pid as string);
                      await intergration.sendMessageToGroup({
                        noti: { title: title, body: title },
                        data: { id: randomGID(), type: NotiType.changeTaskStatus, url: data[0].ToiletServicesId ? `workspace?id=${data[0].ToiletServicesId}` : `project?id=${data[0].ToiletId}` },
                        deviceTokens: deviceTokens,
                      });
                    }
                    notificationRepo.action(
                      "add",
                      customers.map((e: any) => {
                        return {
                          Id: randomGID(),
                          Name: title,
                          DateCreated: Date.now(),
                          Sort: 1,
                          Content: title,
                          Status: 0,
                          Type: NotiType.changeTaskStatus,
                          ToiletServicesId: data[0].ToiletServicesId,
                          ToiletId: data[0].ToiletId,
                          CustomerId: e.Id,
                          LinkWeb: data[0].ToiletServicesId ? `/workspace?id=${data[0].ToiletServicesId}` : `project?id=${data[0].ToiletId}`,
                          LinkApp: "DetailWorkView",
                        };
                      })
                    );
                    break;
                  default:
                    break;
                }
              }
            });
          }
          break;
        case "Ticket":
          const currentTicket = (await _moduleRepo.getById(data[0].Id)) as any;
          if (currentTicket?.Status !== data[0].Status) {
            customerIds.push(...data.map((e: any) => e.CustomerId).filter((e?: string) => e?.length));
            if (customerIds.length) {
              const customerRepo = new crudDA(`data:${pid}:Customer`);
              customerRepo.getBylistId(customerIds).then(async (res: any) => {
                const customers = [...res];
                const deviceTokens = customers
                  .filter((e: any) => e.DeviceToken)
                  .map((e: any) => e.DeviceToken.split(","))
                  .flat(Infinity)
                  .filter((devToken, i, arr) => devToken.length && arr.indexOf(devToken) === i);
                let title = "";
                switch (data[0].Status) {
                  case 0:
                    title = `Ticket ${data[0].Name} đã bị hủy`;
                    break;
                  case 2:
                    title = `Ticket ${data[0].Name} đang được xử lý`;
                    break;
                  case 3:
                    title = `Ticket ${data[0].Name} đã được hoàn thành`;
                    break;
                  case 4:
                    title = `Ticket ${data[0].Name} đã được kết thúc`;
                    break;
                  default:
                    break;
                }
                if (deviceTokens.length) {
                  const intergration = new intergrationDA(pid);
                  await intergration.sendMessageToGroup({
                    noti: { title: title, body: title },
                    data: { id: randomGID(), type: NotiType.changeToiletServiceStatus, url: `manager/ticket` },
                    deviceTokens: deviceTokens,
                  });
                }
                notificationRepo.action(
                  "add",
                  customers.map((e: any) => {
                    return {
                      Id: randomGID(),
                      Name: title,
                      DateCreated: Date.now(),
                      Sort: 1,
                      Content: title,
                      Status: 0,
                      Type: NotiType.changeTicketStatus,
                      CustomerId: e.Id,
                      LinkWeb: `/manager/ticket`,
                    };
                  })
                );
              });
            }
          }
          break;
        case "Contract":
        case "Addendum":
          const toiletServicesRepo2 = new crudDA(`data:${pid}:ToiletServices`);
          toiletServicesRepo2.getById(data[0].ToiletServicesId).then(async (toiletService: any) => {
            const customerRepo = new crudDA(`data:${pid}:Customer`);
            switch (data[0].Status) {
              case 1: // đối tác ký
                const guestData = await customerRepo.search(1, 1, `@Mobile:("*${toiletService.CustomerMobile}*")`);
                if (guestData.data?.[0]) {
                  const toiletOwner = guestData.data[0];
                  const deviceTokens = toiletOwner.DeviceToken?.split(",") ?? [];
                  if (deviceTokens.length) {
                    const intergration = new intergrationDA(pid as string);
                    await intergration.sendMessageToGroup({
                      noti: { title: "Đối tác đã ký và gửi " + data[0].Name + " của đơn hàng " + toiletService.Name, body: "Đối tác đã ký và gửi " + data[0].Name + " của đơn hàng " + toiletService.Name },
                      data: { id: randomGID(), type: module === "Contract" ? NotiType.changeContractStatus : NotiType.changeAddendumStatus, url: `workspace?id=${data[0].ToiletServicesId}` },
                      deviceTokens: deviceTokens,
                    });
                  }
                  notificationRepo.action("add", [
                    {
                      Id: randomGID(),
                      Name: "Đối tác đã ký và gửi " + data[0].Name + " của đơn hàng " + toiletService.Name,
                      DateCreated: Date.now(),
                      Sort: 1,
                      Content: "Đối tác đã ký và gửi " + data[0].Name + " của đơn hàng " + toiletService.Name,
                      Status: 0,
                      Type: module === "Contract" ? NotiType.changeContractStatus : NotiType.changeAddendumStatus,
                      ToiletServicesId: toiletService.Id,
                      CustomerId: toiletOwner.Id,
                      LinkWeb: `/workspace?id=${data[0].ToiletServicesId}`,
                      LinkApp: "DetailWorkView",
                    },
                  ]);
                }
                break;
              case 2: // khách ký
              case 3: // khách từ chối
                const customerData = (await customerRepo.getById(toiletService.CustomerId)) as any;
                if (customerData) {
                  const customers = [customerData];
                  if (customers[0].CompanyProfileId) {
                    const companyRepo = new crudDA(`data:${pid}:CustomerCompany`);
                    const members = (await companyRepo.search(1, 1000, `@CompanyProfileId:{${customers[0].CompanyProfileId}} @Status:[1 1] @Role:(*Coordinator*)`, { RETURN: ["Id", "CustomerId"] })) as any;
                    if (members.data?.length) {
                      const memberIds = members.data.map((e: any) => e.CustomerId);
                      const memberCustomer = await customerRepo.getBylistId(memberIds);
                      customers.push(...memberCustomer);
                    }
                  }
                  const deviceTokens = customers
                    .filter((e: any) => e.DeviceToken)
                    .map((e: any) => e.DeviceToken.split(","))
                    .flat(Infinity)
                    .filter((devToken, i, arr) => devToken.length && arr.indexOf(devToken) === i);
                  var title = "";
                  var notiBody = "";
                  switch (data[0].Status) {
                    case 2:
                      title = `${data[0].Name} đã được khách hàng ký`;
                      notiBody = `${data[0].Name} của đơn hàng ${toiletService.Name} đã được khách hàng ký`;
                      break;
                    case 3:
                      title = `${data[0].Name} đã bị từ chối`;
                      notiBody = `${data[0].Name} của đơn hàng ${toiletService.Name} đã bị từ chối`;
                      break;
                    default:
                      break;
                  }
                  if (deviceTokens.length) {
                    const intergration = new intergrationDA(pid as string);
                    await intergration.sendMessageToGroup({
                      noti: { title: title ?? "", body: notiBody ?? "" },
                      data: { id: randomGID(), type: module === "Contract" ? NotiType.changeContractStatus : NotiType.changeAddendumStatus, url: `workspace?id=${data[0].ToiletServicesId}` },
                      deviceTokens: deviceTokens,
                    });
                  }
                  notificationRepo.action(
                    "add",
                    customers.map((e: any) => {
                      return {
                        Id: randomGID(),
                        Name: title,
                        DateCreated: Date.now(),
                        Sort: 1,
                        Content: notiBody,
                        Status: 0,
                        Type: module === "Contract" ? NotiType.changeContractStatus : NotiType.changeAddendumStatus,
                        ToiletServicesId: toiletService.Id,
                        CustomerId: e.Id,
                        LinkWeb: `/workspace?id=${data[0].ToiletServicesId}`,
                        LinkApp: "DetailWorkView",
                      };
                    })
                  );
                }
                break;
              default:
                break;
            }
          });
          break;
        case "Survey":
          if (data[0].ToiletServicesId) {
            const toiletServicesRepo = new crudDA(`data:${pid}:ToiletServices`);
            toiletServicesRepo.getById(data[0].ToiletServicesId).then(async (toiletServices: any) => {
              if (toiletServices) {
                switch (data[0].Status) {
                  case 0: // reject
                    var title = `Khách hàng đã từ chối thông tin khảo sát đơn hàng ${toiletServices.Name}`;
                    var body = `Khách hàng đã từ chối thông tin khảo sát đơn hàng ${toiletServices.Name}. Bạn có thể xem chi tiết lý do từ chối và chỉnh sửa thông tin khảo sát tại màn hình xử lý công việc.`;
                    break;
                  case 1: // partner send
                    title = `Đối tác đã hoàn thành và gửi thông khảo sát đơn hàng ${toiletServices.Name}`;
                    body = `Đối tác đã hoàn thành và gửi thông tin khảo sát đơn hàng ${toiletServices.Name}. Hãy xác nhận các thông tin trên để đơn hàng được chuyển sang bước tư vấn báo giá.`;
                    break;
                  default:
                    title = "";
                    body = "";
                    break;
                }
                if (title.length && body.length) {
                  const customerRepo = new crudDA(`data:${pid}:Customer`);
                  if (data[0].Status === 0) {
                    var customer: any = await customerRepo.getById(toiletServices.CustomerId);
                    customer = [customer];
                  } else {
                    customer = await customerRepo.search(1, 1, `@Mobile:("*${toiletServices.CustomerMobile}*")`);
                    customer = customer.data?.[0];
                    if (customer.CompanyProfileId) {
                      const companyRepo = new crudDA(`data:${pid}:CustomerCompany`);
                      const members = (await companyRepo.search(1, 1000, `@CompanyProfileId:{${customer.CompanyProfileId}} @Status:[1 1] @Role:(*Coordinator*)`, { RETURN: ["Id", "CustomerId"] })) as any;
                      if (members.data?.length) {
                        const memberIds = members.data.map((e: any) => e.CustomerId);
                        const memberCustomer = await customerRepo.getBylistId(memberIds);
                        customer = [...customer, ...memberCustomer];
                      }
                    }
                  }
                  if (customer) {
                    const customers = customer;
                    const deviceTokens = customers
                      .filter((e: any) => e.DeviceToken)
                      .map((e: any) => e.DeviceToken.split(","))
                      .flat(Infinity)
                      .filter((devToken: string, i: number, arr: Array<string>) => devToken.length && arr.indexOf(devToken) === i);
                    if (deviceTokens.length) {
                      const intergration = new intergrationDA(pid);
                      await intergration.sendMessageToGroup({
                        noti: { title: title, body: body },
                        data: { id: randomGID(), type: NotiType.changeToiletServiceStatus, url: `workspace?id=${data[0].ToiletServicesId}` },
                        deviceTokens: deviceTokens,
                      });
                    }
                    notificationRepo.action(
                      "add",
                      customers.map((e: any) => {
                        return {
                          Id: randomGID(),
                          Name: title,
                          DateCreated: Date.now(),
                          Sort: 1,
                          Content: body,
                          Status: 0,
                          Type: NotiType.changeToiletServiceStatus,
                          ToiletServicesId: data[0].ToiletServicesId,
                          CustomerId: e.Id,
                          LinkWeb: `/workspace?id=${data[0].ToiletServicesId}`,
                          LinkApp: `DetailWorkView`,
                        };
                      })
                    );
                  }
                }
              }
            });
          }
          break;
        case "Role":
          const currentData = (await _moduleRepo.getById(data[0].Id)) as any;
          if (data[0].Status && currentData.Status !== data[0].Status) {
            switch (currentData.Status) {
              case StatusRole.invited:
                const groupRepo = new crudDA(`data:${pid}:Group`);
                groupRepo.getById(currentData.GroupId).then(async (group: any) => {
                  if (group) {
                    const roleInGroup = new crudDA(`data:${pid}:Role`);
                    const members = (await roleInGroup.search(1, 10000, `@GroupId:{${currentData.GroupId}} @Status:[${StatusRole.joined}] @Type:[${RoleType.admin} ${RoleType.subadmin}]`, { RETURN: ["Id", "CustomerId"] })) as any;
                    if (members.data?.length) {
                      const memberIds = members.data.map((e: any) => e.CustomerId);
                      customerIds.push(...memberIds);
                    }
                    if (customerIds.length) {
                      const customerRepo = new crudDA(`data:${pid}:Customer`);
                      let customers = await customerRepo.getBylistId([currentData.CustomerId, ...customerIds]);
                      const invitedCustomer = customers.shift() as any;
                      customers = customers.filter((e: any) => e !== undefined && e !== null);
                      const deviceTokens = customers
                        .filter((e: any) => e.DeviceToken)
                        .map((e: any) => e.DeviceToken.split(","))
                        .flat(Infinity)
                        .filter((devToken, i, arr) => devToken.length && arr.indexOf(devToken) === i);
                      if (deviceTokens.length) {
                        const intergration = new intergrationDA(pid as string);
                        await intergration.sendMessageToGroup({
                          noti: { title: `${invitedCustomer.Name} đã tham gia nhóm ${group.Name}`, body: `${invitedCustomer.Name} đã tham gia nhóm ${group.Name}` },
                          data: { id: randomGID(), type: NotiType.acceptInvite, url: `groups/d?id=${group.Id}` },
                          deviceTokens: deviceTokens,
                        });
                      }
                      notificationRepo.action(
                        "add",
                        customers.map((e: any) => {
                          return {
                            Id: randomGID(),
                            Name: `${invitedCustomer.Name} đã tham gia nhóm ${group.Name}`,
                            DateCreated: Date.now(),
                            Sort: 1,
                            Content: `${invitedCustomer.Name} đã tham gia nhóm ${group.Name}`,
                            Status: 0,
                            Type: NotiType.acceptInvite,
                            CustomerId: e.Id,
                            LinkWeb: `/groups/d?id=${group.Id}`,
                            LinkApp: "GroupIndex",
                          };
                        })
                      );
                    }
                  }
                });
                break;
              case StatusRole.requested:
                customerIds.push(payload!.id, ...data.map((e) => e.CustomerId));
                if (customerIds.length > 1) {
                  const customerRepo = new crudDA(`data:${pid}:Customer`);
                  customerRepo.getBylistId(customerIds).then(async (res: any) => {
                    const customers = [...res];
                    const groupRepo = new crudDA(`data:${pid}:Group`);
                    const group = (await groupRepo.getById(currentData.GroupId)) as any;
                    const deviceTokens = customers
                      .slice(1)
                      .filter((e: any) => e.DeviceToken)
                      .map((e: any) => e.DeviceToken.split(","))
                      .flat(Infinity);
                    if (deviceTokens.length) {
                      const intergration = new intergrationDA(pid as string);
                      await intergration.sendMessageToGroup({
                        noti: { title: `${customers[0].Name} đã chấp nhận yêu cầu tham gia nhóm ${group.Name}`, body: `${customers[0].Name} đã chấp nhận yêu cầu tham gia nhóm ${group.Name}` },
                        data: { id: randomGID(), type: NotiType.acceptRequest, url: `groups/d?id=${group.Id}` },
                        deviceTokens: deviceTokens,
                      });
                    }
                    notificationRepo.action(
                      "add",
                      customers.slice(1).map((e: any) => {
                        return {
                          Id: randomGID(),
                          Name: `${customers[0].Name} đã chấp nhận yêu cầu tham gia nhóm ${group.Name}`,
                          DateCreated: Date.now(),
                          Sort: 1,
                          Content: `${customers[0].Name} đã chấp nhận yêu cầu tham gia nhóm ${group.Name}`,
                          Status: 0,
                          Type: NotiType.acceptRequest,
                          CustomerId: e.Id,
                          LinkWeb: `/groups/d?id=${group.Id}`,
                          LinkApp: "GroupIndex",
                        };
                      })
                    );
                  });
                }
                break;
              default:
                break;
            }
          } else if (currentData.Type !== currentData.Type) {
            customerIds.push(payload!.id, currentData.CustomerId);
            if (customerIds.length > 1) {
              const customerRepo = new crudDA(`data:${pid}:Customer`);
              customerRepo.getBylistId(customerIds).then(async (res: any) => {
                const customers = [...res];
                const groupRepo = new crudDA(`data:${pid}:Group`);
                const group = (await groupRepo.getById(currentData.GroupId)) as any;
                const deviceTokens = customers[1].DeviceToken?.split(",") ?? [];
                switch (currentData.Type) {
                  case RoleType.admin:
                    var title = `${customers[0].Name} chuyển quyền sở hữu nhóm ${group.Name} cho bạn.`;
                    break;
                  case RoleType.subadmin:
                    title = `${customers[0].Name} đã thêm bạn làm quản trị viên nhóm ${group.Name}.`;
                    break;
                  case RoleType.member:
                    title = `${customers[0].Name} đã gỡ quyền quản trị viên nhóm ${group.Name} của bạn.`;
                    break;
                  default:
                    title = "";
                    break;
                }
                if (deviceTokens.length) {
                  const intergration = new intergrationDA(pid as string);
                  await intergration.sendMessageToGroup({
                    noti: { title: title, body: title },
                    data: { id: randomGID(), type: NotiType.groupRole, url: `groups/d?id=${group.Id}` },
                    deviceTokens: deviceTokens,
                  });
                }
                notificationRepo.action("add", [
                  {
                    Id: randomGID(),
                    Name: title,
                    DateCreated: Date.now(),
                    Sort: 1,
                    Content: title,
                    Status: 0,
                    Type: NotiType.groupRole,
                    CustomerId: customers[1].Id,
                    LinkWeb: `/groups/d?id=${group.Id}`,
                    LinkApp: "GroupIndex",
                  },
                ]);
              });
            }
          }
          break;
        default:
          break;
      }
      break;
    case "delete":
      switch (module) {
        case "CustomerCompany":
          const memberData = (await _moduleRepo.getById(ids[0])) as any;
          if (memberData && memberData.CustomerId === payload?.id) {
            _moduleRepo.search(1, 1000, `@CompanyProfileId:{${memberData.CompanyProfileId}} @Status:[1 1] @Role:(*Coordinator* | *Owner*)`, { RETURN: ["Id", "CustomerId"] }).then(async (members: any) => {
              const customerRepo = new crudDA(`data:${pid}:Customer`);
              if (members.data?.length) {
                const memberIds = members.data.map((e: any) => e.CustomerId);
                const memberCustomer = await customerRepo.getBylistId(memberIds);
                const deviceTokens = memberCustomer
                  .filter((e: any) => e.DeviceToken)
                  .map((e: any) => e.DeviceToken.split(","))
                  .flat(Infinity)
                  .filter((devToken, i, arr) => devToken.length && arr.indexOf(devToken) === i);
                const customerData = customerRepo.getById(memberData.CustomerId) as any;
                if (deviceTokens.length) {
                  const intergration = new intergrationDA(pid as string);
                  await intergration.sendMessageToGroup({
                    noti: { title: customerData.Name + " đã từ chối tham gia doanh nghiệp của bạn", body: customerData.Name + " đã từ chối tham gia doanh nghiệp của bạn" },
                    data: { id: randomGID(), type: NotiType.rejectInvite, url: `setting/company` },
                    deviceTokens: deviceTokens,
                  });
                }
                notificationRepo.action(
                  "add",
                  memberCustomer.map((e: any) => {
                    return {
                      Id: randomGID(),
                      Name: customerData.Name + " đã từ chối tham gia doanh nghiệp của bạn",
                      DateCreated: Date.now(),
                      Sort: 1,
                      Content: customerData.Name + " đã từ chối tham gia doanh nghiệp của bạn",
                      Status: 0,
                      Type: NotiType.rejectInvite,
                      CustomerCompanyId: memberData.Id,
                      CustomerId: e.Id,
                      LinkWeb: `/setting/company`,
                      LinkApp: "CompanyView",
                    };
                  })
                );
              }
            });
          }
          break;
        case "Role":
          const currentData = (await _moduleRepo.getById(ids[0])) as any;
          if (currentData && (currentData.CustomerId !== payload?.id || currentData.Status === StatusRole.invited)) {
            switch (currentData.Status) {
              case StatusRole.invited:
                const groupRepo = new crudDA(`data:${pid}:Group`);
                groupRepo.getById(currentData.GroupId).then(async (group: any) => {
                  if (group) {
                    const roleInGroup = new crudDA(`data:${pid}:Role`);
                    const members = (await roleInGroup.search(1, 10000, `@GroupId:{${currentData.GroupId}} @Status:[${StatusRole.joined}] @Type:[${RoleType.admin} ${RoleType.subadmin}]`, { RETURN: ["Id", "CustomerId"] })) as any;
                    if (members.data?.length) {
                      const memberIds = members.data.map((e: any) => e.CustomerId);
                      customerIds.push(...memberIds);
                    }
                    if (customerIds.length) {
                      const customerRepo = new crudDA(`data:${pid}:Customer`);
                      let customers = await customerRepo.getBylistId([currentData.CustomerId, ...customerIds]);
                      const invitedCustomer = customers.shift() as any;
                      customers = customers.filter((e: any) => e !== undefined && e !== null);
                      const deviceTokens = customers
                        .filter((e: any) => e.DeviceToken)
                        .map((e: any) => e.DeviceToken.split(","))
                        .flat(Infinity)
                        .filter((devToken, i, arr) => devToken.length && arr.indexOf(devToken) === i);
                      if (deviceTokens.length) {
                        const intergration = new intergrationDA(pid as string);
                        await intergration.sendMessageToGroup({
                          noti: { title: `${invitedCustomer.Name} đã từ chối tham gia nhóm ${group.Name}`, body: `${invitedCustomer.Name} đã từ chối tham gia nhóm ${group.Name}` },
                          data: { id: randomGID(), type: NotiType.acceptInvite, url: `groups/d?id=${group.Id}` },
                          deviceTokens: deviceTokens,
                        });
                      }
                      notificationRepo.action(
                        "add",
                        customers.map((e: any) => {
                          return {
                            Id: randomGID(),
                            Name: `${invitedCustomer.Name} đã từ chối tham gia nhóm ${group.Name}`,
                            DateCreated: Date.now(),
                            Sort: 1,
                            Content: `${invitedCustomer.Name} đã từ chối tham gia nhóm ${group.Name}`,
                            Status: 0,
                            Type: NotiType.acceptInvite,
                            CustomerId: e.Id,
                            LinkWeb: `/groups/d?id=${group.Id}`,
                            LinkApp: "GroupIndex",
                          };
                        })
                      );
                    }
                  }
                });
                break;
              default:
                customerIds.push(payload!.id, currentData.CustomerId);
                if (customerIds.length > 1) {
                  const customerRepo = new crudDA(`data:${pid}:Customer`);
                  customerRepo.getBylistId(customerIds).then(async (res: any) => {
                    const customers = [...res];
                    const groupRepo = new crudDA(`data:${pid}:Group`);
                    const group = (await groupRepo.getById(currentData.GroupId)) as any;
                    const deviceTokens = customers
                      .slice(1)
                      .filter((e: any) => e.DeviceToken)
                      .map((e: any) => e.DeviceToken.split(","))
                      .flat(Infinity);
                    if (deviceTokens.length) {
                      const intergration = new intergrationDA(pid as string);
                      await intergration.sendMessageToGroup({
                        noti: { title: `${customers[0].Name} đã từ chối yêu cầu tham gia nhóm ${group.Name}`, body: `${customers[0].Name} đã từ chối yêu cầu tham gia nhóm ${group.Name}` },
                        data: { id: randomGID(), type: NotiType.acceptRequest, url: `groups/d?id=${group.Id}` },
                        deviceTokens: deviceTokens,
                      });
                    }
                    notificationRepo.action(
                      "add",
                      customers.slice(1).map((e: any) => {
                        return {
                          Id: randomGID(),
                          Name: `${customers[0].Name} đã từ chối yêu cầu tham gia nhóm ${group.Name}`,
                          DateCreated: Date.now(),
                          Sort: 1,
                          Content: `${customers[0].Name} đã từ chối yêu cầu tham gia nhóm ${group.Name}`,
                          Status: 0,
                          Type: NotiType.acceptRequest,
                          CustomerId: e.Id,
                          LinkWeb: `/groups/d?id=${group.Id}`,
                          LinkApp: "GroupIndex",
                        };
                      })
                    );
                  });
                }
                break;
            }
          }
          break;
        default:
          break;
      }
      break;
    default:
      break;
  }
}
