import { SchemaFieldTypes } from "redis";
import { redis } from "../index"

export default class tableDA {
  private schemaName: string;
  constructor(schemaName: string) {
    this.schemaName = schemaName;
  }

  //#region Index
  async buildIndex(schema: { [p: string]: SchemaFieldTypes }) {
    let _schema: any = {}
    Object.keys(schema).forEach((prop) => {
      _schema[`$.${prop}`] = {
        AS: prop,
        SORTABLE: true,
        type: schema[prop],
      }
    })
    await redis.ft.create(`idx:${this.schemaName}`, _schema, {
      ON: 'JSON',
      PREFIX: `${this.schemaName}:`,
    })
  }

  async removeIndex() {
    try {
      await redis.ft.dropIndex(`idx:${this.schemaName}`)
    } catch (error: any) {
      console.log(error.message);
    }
  }

  async idxInfor() {
    try {
      const result = await redis.ft.info(`idx:${this.schemaName}`)
      return result
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async getInterface() {
    const result = await this.idxInfor()
    let _tbInterface: { [p: string]: SchemaFieldTypes } = {}
    if (result?.attributes) {
      for (let e of (result.attributes as any)) {
        _tbInterface[e.attribute] = e.type
      }
    }
    return _tbInterface
  }
}