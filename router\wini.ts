import { Router } from "express";
import crudDA from "../da/crudDA";
import { SchemaFieldTypes } from "redis";
import tableDA from "../da/tableDA";
import { createDefaultTable, initProjectSettingIdx } from "./setting";
import login from "../function/login";
import { JwtPayload } from "jsonwebtoken";
import { ConfigDomain } from "../da/baseDA";
import intergrationDA from "../da/integrationDA";
import { randomGID } from "../Ultis/convert";
import { adminWiniClientSecret } from "../config";

const router = Router();
const _Login = new login();

const winiModules: Array<{ name: string; schema: { [p: string]: SchemaFieldTypes } }> = [
  {
    name: "Project",
    schema: {
      Id: SchemaFieldTypes.TAG,
      Name: SchemaFieldTypes.TEXT,
      CustomerId: SchemaFieldTypes.TAG,
      DateCreated: SchemaFieldTypes.NUMERIC,
      Description: SchemaFieldTypes.TEXT,
      LogoId: SchemaFieldTypes.TAG,
      Sologan: SchemaFieldTypes.TEXT,
      Sort: SchemaFieldTypes.NUMERIC,
      Domain: SchemaFieldTypes.TAG,
      ClientSecret: SchemaFieldTypes.TAG,
    },
  },
  {
    name: "ProjectCustomer",
    schema: {
      Id: SchemaFieldTypes.TAG,
      ParentId: SchemaFieldTypes.TAG,
      Name: SchemaFieldTypes.TEXT,
      DateCreated: SchemaFieldTypes.NUMERIC,
      Description: SchemaFieldTypes.TEXT,
      Favor: SchemaFieldTypes.TAG,
      CustomerId: SchemaFieldTypes.TAG,
      CustomerEmail: SchemaFieldTypes.TAG,
      ProjectId: SchemaFieldTypes.TAG,
      Status: SchemaFieldTypes.NUMERIC, // 0: inactive, 1: active
      Role: SchemaFieldTypes.NUMERIC, // 0: owner, 1: edit, 2: view
    },
  },
  {
    name: "Customer",
    schema: {
      Id: SchemaFieldTypes.TAG,
      Name: SchemaFieldTypes.TEXT,
      AvatarUrl: SchemaFieldTypes.TEXT,
      Birthday: SchemaFieldTypes.NUMERIC,
      Email: SchemaFieldTypes.TAG,
      Gender: SchemaFieldTypes.NUMERIC,
      Mobile: SchemaFieldTypes.TAG,
      Username: SchemaFieldTypes.TAG,
      Password: SchemaFieldTypes.TAG,
      DateCreated: SchemaFieldTypes.NUMERIC,
      IdUserGoogle: SchemaFieldTypes.TAG,
      DeviceToken: SchemaFieldTypes.TAG,
    },
  },
];

// run first time when redis isReady
function initModuleIndex() {
  winiModules.forEach((e) => {
    const _tbModule = new tableDA(`wini:${e.name}`);
    _tbModule.idxInfor().then(async (res) => {
      if (!res) await _tbModule.buildIndex(e.schema);
    });
  });
}
initModuleIndex();

const unAuthorizedUrl = ["/getById", "/getByIds", "/login"];

router.use(async (req, res, next) => {
  const { module, authorization } = req.headers;
  const _m = module && winiModules.find((e) => e.name === module);
  if (_m) {
    if (unAuthorizedUrl.includes(req.url.substring(0, req.url.indexOf("?") > 0 ? req.url.indexOf("?") : req.url.length))) return next();
    const _webDomain = req.headers.origin?.replace("http://", "")?.replace("https://", "");
    let _checkDomain = ConfigDomain.includes(_webDomain ?? "");
    if (!_checkDomain) {
      const _moduleProject = new crudDA(`wini:Project`);
      const findProject = await _moduleProject.search(1, 1, `@Domain:{${_webDomain?.replace(/\./g, "\\.")}}`, { RETURN: ["Id"] });
      if (findProject.data?.[0]) _checkDomain = true;
    }
    if (_checkDomain) {
      if (authorization?.startsWith("Bearer")) {
        try {
          const now = new Date();
          const payload = _Login.verifyToken(authorization.replace("Bearer", "").trim()) as JwtPayload;
          if (!payload.exp || now.getTime() > payload.exp * 1000) {
            return res.send({ code: 401, message: "Token is expired" });
          } else return next();
        } catch (error) {
          return res.send({ code: 401, message: "Unauthorized" });
        }
      } else return res.send({ code: 401, message: "Unauthorized" });
    }
    return res.status(404).send({ code: 404, message: "Page not found!" });
  }
  return res.send({ code: 404, message: "module is not found!" });
});

//#region unAuthorized
router.get("/getById", async (req, res) => {
  const { module } = req.headers;
  const { id } = req.query;
  const _moduleRepo = new crudDA(`wini:${module}`);
  if (id) {
    const results = await _moduleRepo.getById(id as string);
    return res.status(200).json({ data: results, code: 200, message: "Success" });
  } else return res.send({ code: 404, message: "Missing id" });
});

router.post("/getByIds", async (req, res) => {
  const { module } = req.headers;
  const { ids } = req.body;
  if (ids) {
    if (!ids.length) return res.status(200).json({ data: [], code: 200, message: "Success" });
    const _moduleRepo = new crudDA(`wini:${module}`);
    const results = await _moduleRepo.getBylistId(ids);
    return res.status(200).json({ data: results, code: 200, message: "Success" });
  } else return res.send({ code: 404, message: "Missing ids" });
});

router.post("/login", async (req, res) => {
  const { Username, Password, type, token, deviceToken, ggClientId } = req.body;
  const _moduleRepo = new crudDA(`wini:Customer`);
  switch (type) {
    case "google":
      const integration = new intergrationDA("wini");
      const response = await integration.login({ type, token, deviceToken, ggClientId, clientSecret: adminWiniClientSecret });
      if (response.code === 200 && response.data?.email) {
        const payload = response.data;
        const checkEmail = await _moduleRepo.search(1, 10, `@Email:{${payload.email.replace(/[^a-zA-Z0-9]/g, (v: string) => `\\${v}`)}}`);
        let _customer: any = {};
        _customer.IdUserGoogle = payload.sub ?? "";
        if (checkEmail.count) {
          _customer = { ...checkEmail.data[0], ..._customer };
          if (deviceToken) {
            _customer.DeviceToken = checkEmail.data[0].DeviceToken
              ? [
                  ...checkEmail.data[0].DeviceToken.split(",")
                    .slice(1)
                    .filter((tk: string) => tk !== deviceToken),
                  deviceToken,
                ].join(",")
              : deviceToken;
          }
        } else {
          _customer = {
            ..._customer,
            Id: randomGID(),
            Email: payload.email,
            Name: payload.name,
            AvatarUrl: payload.picture,
            DateCreated: Date.now(),
            DeviceToken: deviceToken,
          };
        }
        await _moduleRepo.action("add", [_customer]);
        const accessToken = _Login.createAccessToken({ id: _customer.Id, email: payload.Email });
        const refreshToken = _Login.createRefreshToken({ id: _customer.Id, email: payload.Email });
        return res.status(200).json({ data: { accessToken, refreshToken }, code: 200, message: "Success" });
      }
      break;
    default:
      const userLogin = (await _moduleRepo.search(1, 10, `@Username:{${Username}}`, { RETURN: ["Id", "UserName", "Password"] })) as any;
      if (!userLogin.count) {
        return res.send({ code: 404, message: "User not found" });
      } else {
        const isMatch = await _Login.verifyPassword(Password, userLogin.data?.[0].Password);
        if (!isMatch) {
          return res.send({ code: 403, message: "Invalid password" });
        } else {
          const payload = { id: userLogin.data[0].Id, username: userLogin.data[0].UserName };
          const accessToken = _Login.createAccessToken(payload);
          const refreshToken = _Login.createRefreshToken(payload);
          return res.status(200).json({ data: { accessToken, refreshToken }, code: 200, message: "Success" });
        }
      }
  }
});

//#region authorized
router.get("/getAll", async (req, res) => {
  const { module } = req.headers;
  const _moduleRepo = new crudDA(`wini:${module}`);
  const results = await _moduleRepo.getAll();
  return res.status(200).json({ data: results, code: 200, message: "Success" });
});

router.post("/getListSimple", async (req, res) => {
  const { module } = req.headers;
  const _moduleRepo = new crudDA(`wini:${module}`);
  const { searchRaw, page, size, returns, sortby } = req.body;
  const results = await _moduleRepo.search(page, size, searchRaw, { RETURN: returns, SORTBY: sortby });
  if (results.code !== 404) {
    return res.status(200).json({ data: results.data, totalCount: results.count, code: 200, message: "Success" });
  } else return res.send(results);
});

router.get("/getCustomerInfor", async (req, res) => {
  const { authorization } = req.headers;
  const _moduleRepo = new crudDA(`wini:Customer`);
  const payload = _Login.verifyToken((authorization ?? "").replace("Bearer", "").trim()) as JwtPayload;
  if (payload?.id) {
    const customer = await _moduleRepo.getById(payload.id);
    return res.status(200).json({ code: 200, message: "Success", data: customer });
  } else {
    return res.send({ code: 401, message: "Unauthorized" });
  }
});

router.post("/action", async (req, res) => {
  const { module } = req.headers;
  const _moduleRepo = new crudDA(`wini:${module}`);
  const { action } = req.query;
  const { data, ids } = req.body;
  switch (`${action}`.toLowerCase()) {
    case "add":
      try {
        await _moduleRepo.action("add", data);
        if (module === "Project") {
          data.forEach((p: any) => {
            initProjectSettingIdx(p.Id);
            createDefaultTable(p.Id);
          });
        }
        return res.status(200).json({ data: data, code: 200, message: "Success" });
      } catch (error) {
        return res.send({ code: 404, message: (error as any).message ?? "" });
      }
    case "edit":
      try {
        await _moduleRepo.action("edit", data);
        if (module === "Project") {
          data.forEach((p: any) => {
            initProjectSettingIdx(p.Id);
          });
        }
        return res.status(200).json({ data: data, code: 200, message: "Success" });
      } catch (error) {
        return res.send({ code: 404, message: (error as any).message ?? "" });
      }
    case "delete":
      try {
        if (module === "Project") {
          for (const _id of ids) {
            const projectData = new crudDA(`data:${_id}`);
            projectData.deleteAll();
            const projectRole = new crudDA(`wini:ProjectCustomer`);
            projectRole.deleteAll();
            const projectTb = new crudDA(`setting:${_id}:table`);
            projectTb.getAll().then(async (result) => {
              result.forEach((e: any) => {
                const _tbRepo = new tableDA(e.Name);
                _tbRepo.removeIndex();
              });
              const projectSetting = new crudDA(`setting:${_id}`);
              projectSetting.deleteAll();
            });
            const intergration = new intergrationDA(_id);
            intergration.removeFirebaseAdmin();
          }
        }
        await _moduleRepo.delete(ids);
        return res.status(200).json({ data: ids, code: 200, message: "Success" });
      } catch (error) {
        return res.send({ code: 404, message: (error as any).message ?? "" });
      }
    default:
      return res.send({ code: 404, message: "Invalid action" });
  }
});

router.post("/group", async (req, res) => {
  const { module } = req.headers;
  const _moduleRepo = new crudDA(`wini:${module}`);
  const { reducers, searchRaw } = req.body;
  const results = await _moduleRepo.aggregate({
    searchRaw: searchRaw,
    query: reducers,
  });
  if (results?.code !== 404) {
    const _totalCount = results.shift();
    if (_totalCount) {
      const _data = results.map((e: Array<any>) => {
        let _jsonItem: { [p: string]: any } = {};
        for (let i = 0; i < e.length; i += 2) {
          if (e[i] === "$") {
            let _parseJson = {};
            try {
              _parseJson = JSON.parse(e[i + 1]);
            } catch (error) {
              console.log("parse $ failed");
            }
            _jsonItem = { ..._jsonItem, ..._parseJson };
          } else {
            _jsonItem[e[i]] = e[i + 1];
          }
        }
        return _jsonItem;
      });
      return res.status(200).json({ data: _data, totalCount: _totalCount, code: 200, message: "Success" });
    } else {
      return res.status(200).json({ data: [], totalCount: 0, code: 200, message: "Success" });
    }
  } else {
    return res.send({ code: 404, message: results.message ?? "group error" });
  }
});

export default router;
