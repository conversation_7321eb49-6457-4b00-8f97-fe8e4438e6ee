import { SchemaFieldTypes } from "redis";
import { randomGID } from "../Ultis/convert";

export enum CustomerRole {
  owner = 0,
  admin = 1,
  collaborator = 2,
  viewer = 3,
}

export const tbSchemaConstructor: { [p: string]: SchemaFieldTypes } = {
  Id: SchemaFieldTypes.TAG,
  Name: SchemaFieldTypes.TAG,
  Description: SchemaFieldTypes.TEXT,
  DateCreated: SchemaFieldTypes.NUMERIC,
};

export const menuSchemaConstructor: { [p: string]: SchemaFieldTypes } = {
  Id: SchemaFieldTypes.TAG,
  Name: SchemaFieldTypes.TAG,
  TableId: SchemaFieldTypes.TAG,
  PageId: SchemaFieldTypes.TAG,
  RoleId: SchemaFieldTypes.TAG,
  Icon: SchemaFieldTypes.TEXT,
  ParentId: SchemaFieldTypes.TAG,
  To: SchemaFieldTypes.TAG,
  DateCreated: SchemaFieldTypes.NUMERIC,
  Sort: SchemaFieldTypes.NUMERIC,
  Description: SchemaFieldTypes.TEXT,
  Setting: SchemaFieldTypes.TEXT,
  Type: SchemaFieldTypes.NUMERIC, // 0: admin wini, 1: digital project, ...
};

export const relSchemaConstructor: { [p: string]: SchemaFieldTypes } = {
  Id: SchemaFieldTypes.TAG,
  TablePK: SchemaFieldTypes.TAG,
  TableFK: SchemaFieldTypes.TAG,
  Column: SchemaFieldTypes.TAG, //nametablepkid
  DeleteFK: SchemaFieldTypes.TAG,
  Query: SchemaFieldTypes.TEXT,
  Shortcut: SchemaFieldTypes.TEXT,
  Setting: SchemaFieldTypes.TEXT,
  Form: SchemaFieldTypes.TEXT,
  DateCreated: SchemaFieldTypes.NUMERIC,
};

export const colSchemaConstructor: { [p: string]: SchemaFieldTypes } = {
  Id: SchemaFieldTypes.TAG,
  Name: SchemaFieldTypes.TAG,
  TableName: SchemaFieldTypes.TAG,
  DataType: SchemaFieldTypes.TAG,
  Query: SchemaFieldTypes.TEXT,
  Sort: SchemaFieldTypes.NUMERIC,
  Setting: SchemaFieldTypes.TEXT,
  Form: SchemaFieldTypes.TEXT,
  DateCreated: SchemaFieldTypes.NUMERIC,
};

export const chartSchemaConstructor: { [p: string]: SchemaFieldTypes } = {
  Id: SchemaFieldTypes.TAG,
  Name: SchemaFieldTypes.TEXT,
  Type: SchemaFieldTypes.TAG,
  TbName: SchemaFieldTypes.TAG,
  Query: SchemaFieldTypes.TEXT,
  Description: SchemaFieldTypes.TEXT,
  DateCreated: SchemaFieldTypes.NUMERIC,
  Setting: SchemaFieldTypes.TEXT,
};

export const formSchemaConstructor: { [p: string]: SchemaFieldTypes } = {
  Id: SchemaFieldTypes.TAG,
  Name: SchemaFieldTypes.TAG,
  TbName: SchemaFieldTypes.TAG,
  DateCreated: SchemaFieldTypes.NUMERIC,
  Description: SchemaFieldTypes.TEXT,
  Type: SchemaFieldTypes.TAG,
  Category: SchemaFieldTypes.NUMERIC, // null|0: add/edit, 1: filter
  Props: SchemaFieldTypes.TEXT,
};

export const cardSchemaConstructor: { [p: string]: SchemaFieldTypes } = {
  Id: SchemaFieldTypes.TAG,
  Name: SchemaFieldTypes.TAG,
  TbName: SchemaFieldTypes.TAG,
  DateCreated: SchemaFieldTypes.NUMERIC,
  Description: SchemaFieldTypes.TEXT,
  Type: SchemaFieldTypes.TAG,
  Props: SchemaFieldTypes.TEXT,
  IsPublic: SchemaFieldTypes.TAG,
};

export const viewSchemaConstructor: { [p: string]: SchemaFieldTypes } = {
  Id: SchemaFieldTypes.TAG,
  Name: SchemaFieldTypes.TAG,
  TbName: SchemaFieldTypes.TAG,
  DateCreated: SchemaFieldTypes.NUMERIC,
  Description: SchemaFieldTypes.TEXT,
  Type: SchemaFieldTypes.TAG,
  Props: SchemaFieldTypes.TEXT,
  IsPublic: SchemaFieldTypes.TAG,
};

export const reportModelSchemaConstructor: { [p: string]: SchemaFieldTypes } = {
  Id: SchemaFieldTypes.TAG,
  Name: SchemaFieldTypes.TAG,
  TbName: SchemaFieldTypes.TAG,
  Query: SchemaFieldTypes.TEXT,
  DateCreated: SchemaFieldTypes.NUMERIC,
  Description: SchemaFieldTypes.TEXT,
  Props: SchemaFieldTypes.TEXT,
};

export const chartCardConstructor: { [p: string]: SchemaFieldTypes } = {
  Id: SchemaFieldTypes.TAG,
  Name: SchemaFieldTypes.TAG,
  TbName: SchemaFieldTypes.TAG,
  Title: SchemaFieldTypes.TEXT,
  Query: SchemaFieldTypes.TEXT,
  Group: SchemaFieldTypes.TEXT,
  Type: SchemaFieldTypes.TAG,
  DateCreated: SchemaFieldTypes.NUMERIC,
  Sort: SchemaFieldTypes.NUMERIC,
  Setting: SchemaFieldTypes.TEXT,
};

export const pageConstructor: { [p: string]: SchemaFieldTypes } = {
  Id: SchemaFieldTypes.TAG,
  Name: SchemaFieldTypes.TAG,
  DateCreated: SchemaFieldTypes.NUMERIC,
  Url: SchemaFieldTypes.TAG,
  Params: SchemaFieldTypes.TEXT,
  State: SchemaFieldTypes.TEXT,
  Sort: SchemaFieldTypes.NUMERIC,
  LayoutId: SchemaFieldTypes.TAG,
};

export const layerConstructor: { [p: string]: SchemaFieldTypes } = {
  Id: SchemaFieldTypes.TAG,
  Name: SchemaFieldTypes.TAG,
  DateCreated: SchemaFieldTypes.NUMERIC,
  Type: SchemaFieldTypes.TAG,
  ParentId: SchemaFieldTypes.TAG,
  Setting: SchemaFieldTypes.TEXT,
  PageId: SchemaFieldTypes.TAG,
  ProjectId: SchemaFieldTypes.TAG,
  LayoutId: SchemaFieldTypes.TAG,
  State: SchemaFieldTypes.TEXT,
};

export const designTokenConstructor: { [p: string]: SchemaFieldTypes } = {
  Id: SchemaFieldTypes.TAG,
  Name: SchemaFieldTypes.TAG,
  DateCreated: SchemaFieldTypes.NUMERIC,
  ParentId: SchemaFieldTypes.TAG,
  Type: SchemaFieldTypes.TAG, // type === group => parentId & value = undefined
  Sort: SchemaFieldTypes.NUMERIC,
  Value: SchemaFieldTypes.TEXT,
};

export const workflowConstructor: { [p: string]: SchemaFieldTypes } = {
  Id: SchemaFieldTypes.TAG,
  Name: SchemaFieldTypes.TAG,
  DateCreated: SchemaFieldTypes.NUMERIC,
  Description: SchemaFieldTypes.TEXT,
  Sort: SchemaFieldTypes.NUMERIC,
};

export const stageConstructor: { [p: string]: SchemaFieldTypes } = {
  Id: SchemaFieldTypes.TAG,
  Name: SchemaFieldTypes.TAG,
  DateCreated: SchemaFieldTypes.NUMERIC,
  Description: SchemaFieldTypes.TEXT,
  Sort: SchemaFieldTypes.NUMERIC,
  WorkflowId: SchemaFieldTypes.TAG,
  ParentId: SchemaFieldTypes.TAG,
  Status: SchemaFieldTypes.NUMERIC,
  AssigneeId: SchemaFieldTypes.TAG,
  CollaboratorId: SchemaFieldTypes.TAG,
  Duration: SchemaFieldTypes.TAG,
  FormId: SchemaFieldTypes.TAG,
};

export const settingStageConstructor: { [p: string]: SchemaFieldTypes } = {
  Id: SchemaFieldTypes.TAG,
  Name: SchemaFieldTypes.TAG,
  DateCreated: SchemaFieldTypes.NUMERIC,
  Description: SchemaFieldTypes.TEXT,
  Sort: SchemaFieldTypes.NUMERIC,
  Value: SchemaFieldTypes.TEXT,
  StageId: SchemaFieldTypes.TAG,
};

export const projectDefaultData = [
  {
    tb: {
      Id: randomGID(),
      Name: "Customer",
      Description: "Project customer infor",
      DateCreated: Date.now(),
    },
    cols: [
      {
        Id: randomGID(),
        Name: "Id",
        TableName: "Customer",
        DataType: "GID",
        DateCreated: Date.now(),
        Sort: 0,
      },
      {
        Id: randomGID(),
        Name: "Name",
        TableName: "Customer",
        DataType: "STRING",
        DateCreated: Date.now(),
        Sort: 1,
      },
      {
        Id: randomGID(),
        Name: "DateCreated",
        TableName: "Customer",
        DataType: "DATETIME",
        DateCreated: Date.now(),
        Sort: 2,
      },
      {
        Id: randomGID(),
        Name: "Description",
        TableName: "Customer",
        DataType: "STRING",
        DateCreated: Date.now(),
        Sort: 3,
      },
      {
        Id: randomGID(),
        Name: "Email",
        TableName: "Customer",
        DataType: "STRING",
        DateCreated: Date.now(),
        Sort: 4,
      },
      {
        Id: randomGID(),
        Name: "Mobile",
        TableName: "Customer",
        DataType: "STRING",
        DateCreated: Date.now(),
        Sort: 5,
      },
      {
        Id: randomGID(),
        Name: "AvatarUrl",
        TableName: "Customer",
        DataType: "STRING",
        DateCreated: Date.now(),
        Sort: 6,
      },
    ],
    index: {
      Id: SchemaFieldTypes.TAG,
      Name: SchemaFieldTypes.TEXT,
      DateCreated: SchemaFieldTypes.NUMERIC,
      Description: SchemaFieldTypes.TEXT,
      Email: SchemaFieldTypes.TEXT,
      Mobile: SchemaFieldTypes.TEXT,
      AvatarUrl: SchemaFieldTypes.TEXT,
    },
  },
  {
    tb: {
      Id: randomGID(),
      Name: "User",
      Description: "Project user infor",
      DateCreated: Date.now(),
    },
    cols: [
      {
        Id: randomGID(),
        Name: "Id",
        TableName: "User",
        DataType: "GID",
        DateCreated: Date.now(),
        Sort: 0,
      },
      {
        Id: randomGID(),
        Name: "Name",
        TableName: "User",
        DataType: "STRING",
        DateCreated: Date.now(),
        Sort: 1,
      },
      {
        Id: randomGID(),
        Name: "DateCreated",
        TableName: "User",
        DataType: "DATETIME",
        DateCreated: Date.now(),
        Sort: 2,
      },
      {
        Id: randomGID(),
        Name: "Description",
        TableName: "User",
        DataType: "STRING",
        DateCreated: Date.now(),
        Sort: 3,
      },
      {
        Id: randomGID(),
        Name: "Email",
        TableName: "User",
        DataType: "STRING",
        DateCreated: Date.now(),
        Sort: 4,
      },
      {
        Id: randomGID(),
        Name: "Mobile",
        TableName: "User",
        DataType: "STRING",
        DateCreated: Date.now(),
        Sort: 5,
      },
      {
        Id: randomGID(),
        Name: "AvatarUrl",
        TableName: "User",
        DataType: "STRING",
        DateCreated: Date.now(),
        Sort: 6,
      },
    ],
    index: {
      Id: SchemaFieldTypes.TAG,
      Name: SchemaFieldTypes.TEXT,
      DateCreated: SchemaFieldTypes.NUMERIC,
      Description: SchemaFieldTypes.TEXT,
      Email: SchemaFieldTypes.TEXT,
      Mobile: SchemaFieldTypes.TEXT,
      AvatarUrl: SchemaFieldTypes.TEXT,
    },
  },
  {
    tb: {
      Id: randomGID(),
      Name: "Language",
      Description: "Project language",
      DateCreated: Date.now(),
    },
    cols: [
      {
        Id: randomGID(),
        Name: "Id",
        TableName: "Language",
        DataType: "GID",
        DateCreated: Date.now(),
        Sort: 0,
      },
      {
        Id: randomGID(),
        Name: "Name",
        TableName: "Language",
        DataType: "STRING",
        DateCreated: Date.now(),
        Sort: 1,
      },
      {
        Id: randomGID(),
        Name: "DateCreated",
        TableName: "Language",
        DataType: "DATETIME",
        DateCreated: Date.now(),
        Sort: 2,
      },
      {
        Id: randomGID(),
        Name: "Description",
        TableName: "Language",
        DataType: "STRING",
        DateCreated: Date.now(),
        Sort: 3,
      },
      {
        Id: randomGID(),
        Name: "File",
        TableName: "Language",
        DataType: "FILE",
        DateCreated: Date.now(),
        Sort: 4,
      },
    ],
    index: {
      Id: SchemaFieldTypes.TAG,
      Name: SchemaFieldTypes.TEXT,
      DateCreated: SchemaFieldTypes.NUMERIC,
      Description: SchemaFieldTypes.TEXT,
      File: SchemaFieldTypes.TAG,
    },
  },
];

export const settingDataContructor = {
  Id: SchemaFieldTypes.TAG,
  Name: SchemaFieldTypes.TAG,
  DateCreated: SchemaFieldTypes.NUMERIC,
  TbName: SchemaFieldTypes.TAG,
  IsPublic: SchemaFieldTypes.TAG,
  Type: SchemaFieldTypes.TAG,
};
